from pathlib import Path

from setuptools import find_namespace_packages, setup


def parse_requirements(file_path: str) -> list[str]:
    """Parse requirements file, handling includes and environment markers."""
    requirements = []
    with Path(file_path).open(encoding="utf-8") as f:
        for line in f:
            line = line.strip()
            if not line or line.startswith(("#", "-")):
                continue

            if line.startswith("-r "):
                referenced_file = line.split(" ", 1)[1]
                referenced_path = Path(file_path).parent / referenced_file
                requirements.extend(parse_requirements(str(referenced_path)))
            else:
                requirements.append(line)
    return requirements


def get_long_description() -> str:
    """Read long description from README."""
    with Path("README.md").open(encoding="utf-8") as f:
        return f.read()


install_requires = parse_requirements("requirements/prod.txt")
assert install_requires, "requirements/prod.txt 依赖为空或解析失败"

setup(
    name="thingsmore",
    version="0.1.0",
    author="Your Name",
    author_email="<EMAIL>",
    description="ThingsMore Application",
    long_description=get_long_description(),
    long_description_content_type="text/markdown",
    packages=find_namespace_packages(
        include=["domain_common*","infrastructure*","domain_common*","services*"], exclude=["tests*", "docs*"]
    ),
    package_data={
        "routes": ["py.typed"],
    },
    python_requires=">=3.12",
    install_requires=install_requires,
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Developers",
        "Programming Language :: Python :: 3.12",
        "Typing :: Typed",
    ],
    entry_points={
        "console_scripts": [
            "thingsmore-cli=routes.thingsmore.cli:main",
        ],
    },
    zip_safe=False,
    include_package_data=True,
)
#  pip install -e .
