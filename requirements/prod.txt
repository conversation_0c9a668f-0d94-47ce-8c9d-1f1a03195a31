fastapi
uvicorn[standard]

# Database
aiomysql
SQLAlchemy
redis

# Configuration
pydantic
pydantic-settings


# Utilities
python-jose[cryptography]
passlib[bcrypt]
python-multipart
aiofiles
httpx
tenacity
prometheus-client


# Time Zone
pytz==2024.1

# Production
gunicorn==21.2.0
uvloop==0.19.0 ; sys_platform != "win32"
httptools==0.6.1
loguru==0.7.3
orjson==3.10.15
aiohttp==3.11.14
openai==1.71.0
asgiref==3.8.1
dynaconf==3.2.11
psutil==7.0.0

msgpack>=1.0.5
prometheus-client>=0.17.0
passlib[bcrypt]>=1.7.4
python-multipart>=0.0.6
dependency-injector>=4.41.0
arq==0.26.3
sqlalchemy
apscheduler==3.11
asyncpg==0.30.0