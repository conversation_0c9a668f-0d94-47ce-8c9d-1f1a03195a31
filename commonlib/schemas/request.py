from datetime import datetime
from typing import Generic, Optional, TypeVar

from pydantic import BaseModel, Field

T = TypeVar("T")


class RequestMeta(BaseModel):
    """
    请求元信息结构体
    """

    request_id: Optional[str] = Field(None, description="请求ID，用于链路追踪")
    timestamp: str = Field(
        default_factory=lambda: datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3],
        description="请求时间戳，精确到毫秒",
    )
    version: str = Field("v1", description="接口版本号")


class BaseRequest(BaseModel, Generic[T]):
    """
    通用请求体，支持泛型业务数据
    """

    meta: RequestMeta = Field(default_factory=RequestMeta, description="请求元信息")
    data: T = Field(..., description="主要业务数据载体")
