from abc import ABCMeta
from threading import RLock


class SingletonMeta(ABCMeta):
    """
    线程安全的单例模式
    """

    _instances = {}
    _init_locks = {}

    def __call__(cls, *args, **kwargs):
        if cls not in cls._instances:
            with cls._get_init_lock(cls):
                if cls not in cls._instances:
                    instance = super().__call__(*args, **kwargs)
                    cls._instances[cls] = instance
        return cls._instances[cls]

    @classmethod
    def _get_init_lock(cls, target_cls):
        if target_cls not in cls._init_locks:
            cls._init_locks[target_cls] = RLock()
        return cls._init_locks[target_cls]
