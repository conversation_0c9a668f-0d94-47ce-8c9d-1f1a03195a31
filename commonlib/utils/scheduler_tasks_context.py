from apscheduler.schedulers.asyncio import AsyncIOScheduler
from commonlib.core.logging.tsif_logging import app_logger
from commonlib.utils.func_toolkit import fullname
from commonlib.utils.singleton import SingletonMeta


class SchedulerTasksMgm(metaclass=SingletonMeta):

    def __init__(self):
        self.register_tasks = set()
        self.scheduler = AsyncIOScheduler()

    def add(
        self,
        *,
        trigger,
        name=None,
        replace_existing=True,
        **trigger_args,
    ):
        def wrapper(func):
            nonlocal name
            if not name:
                name = fullname(func)
            if name in self.register_tasks:
                raise KeyError(f"scheduler_mgm add fail, task.{name} exist.")
            self.register_tasks.add(name)
            self.scheduler.add_job(
                func,
                trigger=trigger,
                name=name,
                replace_existing=replace_existing,
                **trigger_args,
            )
            app_logger.info(f"scheduler_mgm add task:{name}.")

        return wrapper

    def remove(self, task_id: str):
        try:
            self.scheduler.remove_job(task_id)
            self.register_tasks.discard(task_id)
            app_logger.info(f"[Scheduler] Removed task: {task_id}")
        except Exception as e:
            app_logger.error(
                f"[Scheduler] Failed to remove task: {task_id} - {e}", exception=True
            )

    def start(self):
        self.scheduler.start()
        app_logger.info(f"[Scheduler] Started with tasks: {self.register_tasks}")

    def stop(self):
        self.scheduler.shutdown()
        app_logger.info("[Scheduler] Shutdown complete")
        self.scheduler = None
        self.register_tasks = None
        app_logger.info("Stopping scheduler...")


scheduler_tasks_mgm = SchedulerTasksMgm()
