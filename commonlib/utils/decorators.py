import time
from asyncio import iscoroutinefunction
from functools import wraps
from typing import Any

from commonlib.core.logging.tsif_logging import app_logger
from commonlib.utils.func_toolkit import fullname


def time_costs(func: Any):
    """
    计时装饰器，同时确保有correlation_id
    """

    @wraps(func)
    async def wrapper_async_func(*args, **kwargs):
        start = time.perf_counter()
        result = await func(*args, **kwargs)
        elapsed_time = time.perf_counter() - start

        app_logger.info(
            f"function.elapsed.time.{fullname(func)}: {elapsed_time}s, with parameters {args} and {kwargs}"
        )
        return result

    @wraps(func)
    def wrapper_sync_func(*args, **kwargs):
        start = time.perf_counter()
        result = func(*args, **kwargs)
        elapsed_time = time.perf_counter() - start

        app_logger.info(
            f"function.elapsed.time.{fullname(func)}: {elapsed_time}s, with parameters {args} and {kwargs}"
        )
        return result

    return wrapper_async_func if iscoroutinefunction(func) else wrapper_sync_func


def catch_exception(func: Any):

    @wraps(func)
    async def wrapper_async_func(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            app_logger.error(
                f"{fullname(func)} | args: {args} | kwargs:{kwargs} | error:{str(e)}",
                exception=True,
            )

    @wraps(func)
    def wrapper_sync_func(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            app_logger.error(
                f"{fullname(func)} | args: {args} | kwargs:{kwargs} | error:{str(e)}",
                exception=True,
            )

    return wrapper_async_func if iscoroutinefunction(func) else wrapper_sync_func
