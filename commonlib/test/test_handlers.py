#!/usr/bin/env python3
"""
异常处理器测试脚本
验证异常处理器是否按照响应体规范正确工作
"""

import asyncio
import json
import os
from unittest.mock import Mock, patch

from commonlib.exceptions.exception_handlers import (api_error_handler,
                                                     service_exception_handler,
                                                     validation_handler,
                                                     value_error_handler)
from commonlib.exceptions.exceptions import (DocumentProcessingError,
                                             ServerException, ValidationError)
from fastapi import Request
from fastapi.exceptions import RequestValidationError


def create_mock_request(method: str = "GET", path: str = "/test") -> Request:
    """创建模拟的FastAPI请求对象"""
    request = Mock(spec=Request)
    request.method = method
    request.url = Mock()
    request.url.path = path
    return request


async def test_api_error_handler():
    """测试API异常处理器"""
    print("🧪 测试API异常处理器...")

    # 测试验证错误
    validation_error = ValidationError(
        "邮箱格式不正确",
        field_name="email",
        field_value="invalid@",
        validation_type="email",
    )

    request = create_mock_request("POST", "/api/users")
    response = await api_error_handler(request, validation_error)

    assert response.status_code == 400
    content = json.loads(response.body.decode())

    # 验证响应结构
    assert content["status"] == "fail"
    assert content["code"] == 400
    assert "message" in content
    assert "timestamp" in content
    assert "request_id" in content

    print("✅ 验证错误处理正确")

    # 测试服务器错误
    server_error = ServerException("数据库连接失败", reference="ERR-2024-001")

    response = await api_error_handler(request, server_error)
    assert response.status_code == 500
    content = json.loads(response.body.decode())

    assert content["status"] == "error"
    assert content["code"] == 500

    print("✅ 服务器错误处理正确")

    # 测试RAG项目异常
    doc_error = DocumentProcessingError(
        "PDF解析失败", file_name="document.pdf", file_type="pdf"
    )

    response = await api_error_handler(request, doc_error)
    assert response.status_code == 400
    content = json.loads(response.body.decode())

    assert content["status"] == "fail"
    print("✅ RAG项目异常处理正确")


async def test_validation_handler():
    """测试验证异常处理器"""
    print("\n🧪 测试验证异常处理器...")

    # 模拟RequestValidationError
    validation_error = Mock(spec=RequestValidationError)
    validation_error.errors.return_value = [
        {
            "type": "value_error.missing",
            "loc": ["body", "email"],
            "msg": "field required",
        },
        {
            "type": "value_error.email",
            "loc": ["body", "email"],
            "msg": "invalid email format",
        },
    ]

    request = create_mock_request("POST", "/api/register")
    response = await validation_handler(request, validation_error)

    assert response.status_code == 422
    content = json.loads(response.body.decode())

    # 验证响应结构
    assert content["status"] == "fail"
    assert content["code"] == 422
    assert "message" in content
    assert "timestamp" in content

    print("✅ 请求验证错误处理正确")


async def test_service_exception_handler():
    """测试全局异常处理器"""
    print("\n🧪 测试全局异常处理器...")

    # 测试普通异常
    exception = Exception("Unexpected error occurred")
    request = create_mock_request("GET", "/api/data")

    response = await service_exception_handler(request, exception)

    assert response.status_code == 500
    content = json.loads(response.body.decode())

    # 验证响应结构
    assert content["status"] == "error"
    assert content["code"] == 500
    assert content["message"] == "Internal server error"
    assert "timestamp" in content
    assert "request_id" in content

    print("✅ 全局异常处理正确")


async def test_value_error_handler():
    """测试ValueError处理器"""
    print("\n🧪 测试ValueError处理器...")

    value_error = ValueError("Invalid parameter: age must be positive")
    request = create_mock_request("PUT", "/api/users/123")

    response = await value_error_handler(request, value_error)

    assert response.status_code == 400
    content = json.loads(response.body.decode())

    # 验证响应结构
    assert content["status"] == "fail"
    assert content["code"] == 400
    assert content["message"] == "Invalid parameter value"

    print("✅ ValueError处理正确")


def test_response_structure():
    """测试响应结构是否符合规范"""
    print("\n🧪 测试响应结构规范...")

    # 这里我们验证响应结构是否符合schemas/responses.py中的定义
    from commonlib.schemas.responses import (ErrorDetail, ErrorResponse,
                                             FailResponse)

    # 测试FailResponse结构
    fail_response = FailResponse(
        data=None,
        message="Test fail message",
        errors=[ErrorDetail(type="test_error", message="Test error message")],
        code=400,
        request_id="test-123",
    )

    fail_dict = fail_response.model_dump(exclude_none=True)

    # 验证必需字段
    required_fields = ["status", "code", "message", "timestamp", "request_id"]
    for field in required_fields:
        assert field in fail_dict, f"Missing required field: {field}"

    assert fail_dict["status"] == "fail"
    assert fail_dict["code"] == 400

    print("✅ FailResponse结构正确")

    # 测试ErrorResponse结构
    error_response = ErrorResponse(
        data=None,
        message="Test error message",
        errors=[ErrorDetail(type="internal_error", message="Internal error")],
        code=500,
        request_id="test-456",
    )

    error_dict = error_response.model_dump(exclude_none=True)

    for field in required_fields:
        assert field in error_dict, f"Missing required field: {field}"

    assert error_dict["status"] == "error"
    assert error_dict["code"] == 500

    print("✅ ErrorResponse结构正确")


async def run_all_tests():
    """运行所有测试"""
    print("🚀 开始异常处理器测试...\n")

    # 设置测试环境
    os.environ["ENVIRONMENT"] = "development"

    # 模拟request_id上下文
    with patch(
        "commonlib.schemas.responses.get_request_id", return_value="test-request-123"
    ):
        try:
            await test_api_error_handler()
            await test_validation_handler()
            await test_service_exception_handler()
            await test_value_error_handler()
            test_response_structure()

            print("\n🎉 所有测试通过！异常处理器工作正常")
            print("\n📋 测试总结:")
            print("✅ API异常处理器 - 正确处理自定义异常")
            print("✅ 验证异常处理器 - 正确处理请求验证错误")
            print("✅ 全局异常处理器 - 正确处理未捕获异常")
            print("✅ ValueError处理器 - 正确处理值错误")
            print("✅ 响应结构规范 - 符合schemas定义")

        except Exception as e:
            print(f"\n❌ 测试失败: {str(e)}")
            import traceback

            traceback.print_exc()
            return False

    return True


if __name__ == "__main__":
    success = asyncio.run(run_all_tests())
    if not success:
        exit(1)

    print("\n🎯 异常处理器已准备就绪，可以集成到FastAPI应用中！")
    print("\n📖 使用方法:")
    print("```python")
    print(
        "from commonlib.exceptions.exception_handlers import register_exception_handlers"
    )
    print("from fastapi import FastAPI")
    print("")
    print("app = FastAPI()")
    print("register_exception_handlers(app)")
    print("```")
