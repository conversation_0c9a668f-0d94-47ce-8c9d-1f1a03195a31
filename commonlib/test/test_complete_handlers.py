#!/usr/bin/env python3
"""
完整异常处理器测试脚本
验证所有异常处理器是否正常工作
"""

import asyncio
import json
import os
from unittest.mock import Mock, patch

from commonlib.exceptions.exception_handlers import (
    api_error_handler, app_exception_handlers, attribute_error_handler,
    connection_error_handler, file_not_found_error_handler,
    get_exception_handler_info, get_exception_summary, http_exception_handler,
    json_decode_error_handler, key_error_handler, permission_error_handler,
    register_exception_handlers, service_exception_handler,
    timeout_error_handler, type_error_handler, validation_handler,
    value_error_handler)
from commonlib.exceptions.exceptions import (DatabaseError,
                                             DocumentProcessingError,
                                             UnauthorizedError,
                                             ValidationError)
from fastapi import HTTPException, Request
from fastapi.exceptions import RequestValidationError


def create_mock_request(method: str = "GET", path: str = "/test") -> Request:
    """创建模拟的FastAPI请求对象"""
    request = Mock(spec=Request)
    request.method = method
    request.url = Mock()
    request.url.path = path
    return request


async def test_handler(handler_func, exception, handler_name: str):
    """测试单个异常处理器"""
    try:
        request = create_mock_request("POST", "/api/test")

        with patch(
            "commonlib.schemas.responses.get_request_id", return_value="test-123"
        ):
            response = await handler_func(request, exception)

        # 验证响应
        assert response.status_code >= 200
        assert hasattr(response, "body")

        # 解析响应内容
        content = json.loads(response.body.decode())

        # 验证响应结构
        required_fields = ["status", "code", "message", "timestamp", "request_id"]
        for field in required_fields:
            assert field in content, f"Missing field {field} in {handler_name} response"

        # 验证状态
        assert content["status"] in [
            "success",
            "fail",
            "error",
        ], f"Invalid status in {handler_name}"

        return (
            True,
            f"✅ {handler_name} - 状态码: {response.status_code}, 状态: {content['status']}",
        )

    except Exception as e:
        return False, f"❌ {handler_name} - 错误: {str(e)}"


async def test_all_handlers():
    """测试所有异常处理器"""
    print("🧪 开始完整异常处理器测试...\n")

    # 设置测试环境
    os.environ["ENVIRONMENT"] = "development"

    test_cases = [
        # API异常处理器
        (
            api_error_handler,
            ValidationError("测试验证错误", field_name="email"),
            "API Error Handler",
        ),
        (api_error_handler, UnauthorizedError("认证失败"), "API Error Handler (Auth)"),
        (
            api_error_handler,
            DatabaseError("数据库连接失败"),
            "API Error Handler (Database)",
        ),
        (
            api_error_handler,
            DocumentProcessingError("PDF解析失败", "test.pdf"),
            "API Error Handler (RAG)",
        ),
        # 验证异常处理器
        (validation_handler, create_mock_validation_error(), "Validation Handler"),
        # HTTP异常处理器
        (
            http_exception_handler,
            HTTPException(status_code=404, detail="Not found"),
            "HTTP Exception Handler",
        ),
        (
            http_exception_handler,
            HTTPException(status_code=500, detail="Internal error"),
            "HTTP Exception Handler (500)",
        ),
        # 标准Python异常处理器
        (value_error_handler, ValueError("无效的参数值"), "Value Error Handler"),
        (key_error_handler, KeyError("missing_key"), "Key Error Handler"),
        (type_error_handler, TypeError("类型不匹配"), "Type Error Handler"),
        (
            attribute_error_handler,
            AttributeError("对象缺少属性"),
            "Attribute Error Handler",
        ),
        # JSON异常处理器
        (
            json_decode_error_handler,
            json.JSONDecodeError("Invalid JSON", '{"invalid": }', 12),
            "JSON Decode Error Handler",
        ),
        # 网络和系统异常处理器
        (
            timeout_error_handler,
            asyncio.TimeoutError("操作超时"),
            "Timeout Error Handler",
        ),
        (
            connection_error_handler,
            ConnectionError("连接失败"),
            "Connection Error Handler",
        ),
        (
            permission_error_handler,
            PermissionError("权限不足"),
            "Permission Error Handler",
        ),
        (
            file_not_found_error_handler,
            FileNotFoundError("文件不存在"),
            "File Not Found Error Handler",
        ),
        # 全局异常处理器
        (service_exception_handler, Exception("未知异常"), "Service Exception Handler"),
        (
            service_exception_handler,
            RuntimeError("运行时错误"),
            "Service Exception Handler (Runtime)",
        ),
    ]

    passed = 0
    failed = 0
    results = []

    for handler_func, exception, handler_name in test_cases:
        success, message = await test_handler(handler_func, exception, handler_name)
        results.append(message)

        if success:
            passed += 1
        else:
            failed += 1

    # 输出结果
    print("📊 测试结果:")
    print("=" * 80)
    for result in results:
        print(result)

    print("\n" + "=" * 80)
    print(f"✅ 通过: {passed}")
    print(f"❌ 失败: {failed}")
    print(f"📈 成功率: {passed/(passed+failed)*100:.1f}%")

    return failed == 0


def create_mock_validation_error():
    """创建模拟的验证错误"""
    validation_error = Mock(spec=RequestValidationError)
    validation_error.errors.return_value = [
        {
            "type": "value_error.missing",
            "loc": ["body", "email"],
            "msg": "field required",
        },
        {
            "type": "value_error.email",
            "loc": ["body", "email"],
            "msg": "invalid email format",
        },
    ]
    return validation_error


def test_handler_registration():
    """测试异常处理器注册功能"""
    print("\n🔧 测试异常处理器注册功能...")

    # 创建模拟的FastAPI应用
    mock_app = Mock()
    mock_app.add_exception_handler = Mock()

    # 测试注册功能
    with patch("commonlib.exceptions.exception_handlers.app_logger"):
        register_exception_handlers(mock_app)

    # 验证注册调用次数
    expected_calls = len(app_exception_handlers)
    actual_calls = mock_app.add_exception_handler.call_count

    assert (
        actual_calls == expected_calls
    ), f"Expected {expected_calls} calls, got {actual_calls}"

    print(f"✅ 异常处理器注册测试通过 - 注册了 {actual_calls} 个处理器")


def test_utility_functions():
    """测试工具函数"""
    print("\n🛠️ 测试工具函数...")

    # 测试异常处理器信息获取
    handler_info = get_exception_handler_info()
    assert "total_handlers" in handler_info
    assert "handlers" in handler_info
    assert handler_info["total_handlers"] > 0

    print(
        f"✅ 异常处理器信息获取测试通过 - 发现 {handler_info['total_handlers']} 个处理器"
    )

    # 测试系统摘要获取
    summary = get_exception_summary()
    assert "version" in summary
    assert "total_handlers" in summary
    assert "features" in summary

    print(f"✅ 系统摘要获取测试通过 - 版本: {summary['version']}")


async def run_all_tests():
    """运行所有测试"""
    print("🚀 开始完整异常处理器系统测试...\n")

    try:
        # 测试所有异常处理器
        handlers_success = await test_all_handlers()

        # 测试注册功能
        test_handler_registration()

        # 测试工具函数
        test_utility_functions()

        if handlers_success:
            print("\n🎉 所有测试通过！异常处理器系统工作正常")

            # 显示系统信息
            summary = get_exception_summary()
            print(f"\n📋 系统摘要:")
            print(f"  版本: {summary['version']}")
            print(f"  处理器总数: {summary['total_handlers']}")
            print(f"  运行环境: {summary['environment']}")
            print(f"  功能特性: {len(summary['features'])} 项")

            return True
        else:
            print("\n❌ 部分测试失败")
            return False

    except Exception as e:
        print(f"\n💥 测试过程中发生错误: {str(e)}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = asyncio.run(run_all_tests())

    if success:
        print("\n🎯 异常处理器系统已准备就绪！")
        print("\n📖 使用方法:")
        print("```python")
        print(
            "from commonlib.exceptions.exception_handlers import register_exception_handlers"
        )
        print("from fastapi import FastAPI")
        print("")
        print("app = FastAPI()")
        print("register_exception_handlers(app)")
        print("```")
        exit(0)
    else:
        exit(1)
