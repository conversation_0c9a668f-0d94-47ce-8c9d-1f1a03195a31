from typing import Any, Dict, List, Optional, Type, TypeVar

from sqlalchemy import and_, delete, func, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload, selectinload

T = TypeVar("T")


class AsyncCRUDRepository:
    def __init__(self, model: Type[T]):
        """
        初始化异步CRUD操作类

        :param model: 继承自BaseEntity的SQLAlchemy模型类
        """
        self.model = model

    async def get(
        self,
        db: AsyncSession,
        id: int,
        *,
        include_deleted: bool = False,
        load_relationships: Optional[List[str]] = None,
        lock: bool = False,
    ) -> Optional[T]:
        """
        获取单个实体

        :param db: 异步数据库会话
        :param id: 实体ID
        :param include_deleted: 是否包含已删除实体
        :param load_relationships: 需要立即加载的关系属性名列表
        :param lock: 是否加锁(用于事务)
        :return: 实体实例或None
        """
        stmt = select(self.model).where(self.model.id == id)

        # 应用软删除过滤
        if not include_deleted:
            stmt = self.model.filter_active(stmt)

        # 应用关系加载
        if load_relationships:
            for relation in load_relationships:
                if hasattr(self.model, relation):
                    stmt = stmt.options(selectinload(getattr(self.model, relation)))

        # 应用行锁
        if lock:
            stmt = stmt.with_for_update()

        result = await db.execute(stmt)
        return result.scalar_one_or_none()

    async def get_multi(
        self,
        db: AsyncSession,
        *,
        skip: int = 0,
        limit: int = 100,
        include_deleted: bool = False,
        filters: Optional[List[Any]] = None,
        order_by: Optional[str] = None,
        load_relationships: Optional[List[str]] = None,
        use_joinedload: bool = False,
    ) -> List[T]:
        """
        获取多个实体

        :param db: 异步数据库会话
        :param skip: 跳过记录数
        :param limit: 返回记录数
        :param include_deleted: 是否包含已删除实体
        :param filters: 附加过滤条件列表
        :param order_by: 排序字段名
        :param load_relationships: 需要立即加载的关系属性名列表
        :param use_joinedload: 是否使用joinedload替代selectinload
        :return: 实体列表
        """
        stmt = select(self.model)

        # 应用软删除过滤
        if not include_deleted:
            stmt = self.model.filter_active(stmt)

        # 应用附加过滤条件
        if filters:
            stmt = stmt.where(and_(*filters))

        # 应用排序
        if order_by and hasattr(self.model, order_by):
            stmt = stmt.order_by(getattr(self.model, order_by))

        # 应用分页
        stmt = stmt.offset(skip).limit(limit)

        # 应用关系加载
        if load_relationships:
            for relation in load_relationships:
                if hasattr(self.model, relation):
                    loader = joinedload if use_joinedload else selectinload
                    stmt = stmt.options(loader(getattr(self.model, relation)))

        # 应用模型默认查询选项
        stmt = stmt.execution_options(**self.model.get_query_options())

        result = await db.execute(stmt)
        return result.scalars().all()

    async def create(
        self,
        db: AsyncSession,
        *,
        obj_in: Dict[str, Any],
        commit: bool = True,
        refresh: bool = True,
    ) -> T:
        """
        创建新实体

        :param db: 异步数据库会话
        :param obj_in: 创建数据字典
        :param commit: 是否立即提交
        :param refresh: 是否刷新获取最新状态
        :return: 新创建的实体实例
        """
        db_obj = self.model(**obj_in)
        db.add(db_obj)

        if commit:
            await db.commit()
            if refresh:
                await db.refresh(db_obj)

        return db_obj

    async def update(
        self,
        db: AsyncSession,
        *,
        db_obj: T,
        obj_in: Dict[str, Any],
        commit: bool = True,
        refresh: bool = True,
    ) -> T:
        """
        更新实体

        :param db: 异步数据库会话
        :param db_obj: 要更新的数据库对象
        :param obj_in: 更新数据字典
        :param commit: 是否立即提交
        :param refresh: 是否刷新获取最新状态
        :return: 更新后的实体实例
        """
        for field, value in obj_in.items():
            if hasattr(db_obj, field):
                setattr(db_obj, field, value)

        db.add(db_obj)

        if commit:
            await db.commit()
            if refresh:
                await db.refresh(db_obj)

        return db_obj

    async def soft_delete(
        self,
        db: AsyncSession,
        *,
        id: Optional[int] = None,
        db_obj: Optional[T] = None,
        commit: bool = True,
        refresh: bool = True,
    ) -> Optional[T]:
        """
        软删除实体

        :param db: 异步数据库会话
        :param id: 实体ID(与db_obj二选一)
        :param db_obj: 实体对象(与id二选一)
        :param commit: 是否立即提交
        :param refresh: 是否刷新获取最新状态
        :return: 已删除的实体实例或None
        """
        if db_obj is None and id is not None:
            db_obj = await self.get(db, id=id, include_deleted=True)

        if db_obj is None:
            return None

        db_obj.soft_delete()
        db.add(db_obj)

        if commit:
            await db.commit()
            if refresh:
                await db.refresh(db_obj)

        return db_obj

    async def restore(
        self,
        db: AsyncSession,
        *,
        id: Optional[int] = None,
        db_obj: Optional[T] = None,
        commit: bool = True,
        refresh: bool = True,
    ) -> Optional[T]:
        """
        恢复已删除实体

        :param db: 异步数据库会话
        :param id: 实体ID(与db_obj二选一)
        :param db_obj: 实体对象(与id二选一)
        :param commit: 是否立即提交
        :param refresh: 是否刷新获取最新状态
        :return: 已恢复的实体实例或None
        """
        if db_obj is None and id is not None:
            db_obj = await self.get(db, id=id, include_deleted=True)

        if db_obj is None or db_obj.deleted_at is None:
            return None

        db_obj.deleted_at = None
        db.add(db_obj)

        if commit:
            await db.commit()
            if refresh:
                await db.refresh(db_obj)

        return db_obj

    async def remove(
        self,
        db: AsyncSession,
        *,
        id: Optional[int] = None,
        db_obj: Optional[T] = None,
        commit: bool = True,
    ) -> bool:
        """
        硬删除实体

        :param db: 异步数据库会话
        :param id: 实体ID(与db_obj二选一)
        :param db_obj: 实体对象(与id二选一)
        :param commit: 是否立即提交
        :return: 是否删除成功
        """
        if db_obj is None and id is not None:
            stmt = delete(self.model).where(self.model.id == id)
        elif db_obj is not None:
            stmt = delete(self.model).where(self.model.id == db_obj.id)
        else:
            return False

        result = await db.execute(stmt)

        if commit:
            await db.commit()

        return result.rowcount > 0

    async def exists(
        self,
        db: AsyncSession,
        *,
        id: Optional[int] = None,
        filters: Optional[List[Any]] = None,
        include_deleted: bool = False,
    ) -> bool:
        """
        检查实体是否存在

        :param db: 异步数据库会话
        :param id: 实体ID
        :param filters: 附加过滤条件列表
        :param include_deleted: 是否包含已删除实体
        :return: 是否存在
        """
        stmt = select(self.model).limit(1)

        if id is not None:
            stmt = stmt.where(self.model.id == id)

        if not include_deleted:
            stmt = self.model.filter_active(stmt)

        if filters:
            stmt = stmt.where(and_(*filters))

        result = await db.execute(stmt)
        return result.scalar_one_or_none() is not None

    async def count(
        self,
        db: AsyncSession,
        *,
        filters: Optional[List[Any]] = None,
        include_deleted: bool = False,
    ) -> int:
        """
        统计实体数量

        :param db: 异步数据库会话
        :param filters: 附加过滤条件列表
        :param include_deleted: 是否包含已删除实体
        :return: 数量
        """
        stmt = select(func.count()).select_from(self.model)

        if not include_deleted:
            stmt = self.model.filter_active(stmt)

        if filters:
            stmt = stmt.where(and_(*filters))

        result = await db.execute(stmt)
        return result.scalar_one()
