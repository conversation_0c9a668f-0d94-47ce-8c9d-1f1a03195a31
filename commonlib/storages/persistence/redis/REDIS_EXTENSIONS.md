# Redis仓库扩展方法文档

本文档描述了为支持JWT管理器和其他高级功能而新增的Redis仓库方法。

## 📋 新增方法列表

### 🔑 基础键操作

| 方法 | 描述 | 参数 | 返回值 |
|------|------|------|--------|
| `keys(pattern)` | 获取匹配模式的所有键 | `pattern: str` | `List[str]` |
| `expire(key, seconds)` | 设置键的过期时间 | `key: str, seconds: int` | `bool` |
| `ttl(key)` | 获取键的剩余生存时间 | `key: str` | `int` |
| `incr(key, amount=1)` | 递增键的值 | `key: str, amount: int` | `int` |
| `decr(key, amount=1)` | 递减键的值 | `key: str, amount: int` | `int` |

### 📝 列表操作

| 方法 | 描述 | 参数 | 返回值 |
|------|------|------|--------|
| `rpush(key, *values)` | 从右侧推入列表 | `key: str, *values: str` | `int` |
| `lpop(key)` | 从左侧弹出列表元素 | `key: str` | `Optional[str]` |
| `llen(key)` | 获取列表长度 | `key: str` | `int` |
| `ltrim(key, start, stop)` | 修剪列表到指定范围 | `key: str, start: int, stop: int` | `bool` |

### 🎯 集合操作

| 方法 | 描述 | 参数 | 返回值 |
|------|------|------|--------|
| `sismember(key, member)` | 检查成员是否在集合中 | `key: str, member: str` | `bool` |
| `scard(key)` | 获取集合大小 | `key: str` | `int` |

### 📦 批量操作

| 方法 | 描述 | 参数 | 返回值 |
|------|------|------|--------|
| `mget(keys)` | 批量获取多个键的值 | `keys: List[str]` | `List[Optional[str]]` |
| `mset(mapping)` | 批量设置多个键值对 | `mapping: Dict[str, Any]` | `bool` |
| `delete_pattern(pattern)` | 删除匹配模式的所有键 | `pattern: str` | `int` |

### 🔧 数据库操作

| 方法 | 描述 | 参数 | 返回值 |
|------|------|------|--------|
| `flushdb()` | 清空当前数据库 | 无 | `bool` |
| `ping()` | 测试连接 | 无 | `bool` |
| `info(section=None)` | 获取Redis服务器信息 | `section: Optional[str]` | `Dict[str, Any]` |

### 🔄 事务操作

| 方法 | 描述 | 参数 | 返回值 |
|------|------|------|--------|
| `watch(*keys)` | 监视键的变化 | `*keys: str` | `bool` |
| `unwatch()` | 取消监视所有键 | 无 | `bool` |
| `multi()` | 开始事务 | 无 | `Any` |
| `execute()` | 执行事务 | 无 | `Any` |

### 📢 发布订阅

| 方法 | 描述 | 参数 | 返回值 |
|------|------|------|--------|
| `publish(channel, message)` | 发布消息到频道 | `channel: str, message: str` | `int` |
| `subscribe(*channels)` | 订阅频道 | `*channels: str` | `Any` |

### 🛠️ 工具方法

| 方法 | 描述 | 参数 | 返回值 |
|------|------|------|--------|
| `clear_all()` | 清理所有带前缀的键 | 无 | `bool` |

## 🎯 使用场景

### 1. JWT令牌管理

```python
# 令牌黑名单
await redis_repo.set(f"blacklist_token:{token_hash}", revoke_info, ttl=3600)
is_blacklisted = await redis_repo.exists(f"blacklist_token:{token_hash}")

# 查找刷新令牌
token_keys = await redis_repo.keys("refresh_token:*")
for key in token_keys:
    token_info = await redis_repo.get(key)
```

### 2. 审计日志管理

```python
# 添加日志条目
await redis_repo.lpush(f"audit_log:{user_id}", log_entry)

# 限制日志数量（只保留最近100条）
await redis_repo.ltrim(f"audit_log:{user_id}", 0, 99)

# 获取日志数量
log_count = await redis_repo.llen(f"audit_log:{user_id}")
```

### 3. 用户会话管理

```python
# 添加会话
await redis_repo.sadd(f"user_sessions:{user_id}", session_id)

# 检查会话数量
session_count = await redis_repo.scard(f"user_sessions:{user_id}")

# 检查特定会话
session_exists = await redis_repo.sismember(f"user_sessions:{user_id}", session_id)
```

### 4. 计数器和统计

```python
# 登录计数
await redis_repo.incr("daily_logins")

# 失败重试计数
retry_count = await redis_repo.incr(f"login_failures:{user_id}")
if retry_count > 5:
    await redis_repo.expire(f"login_failures:{user_id}", 3600)  # 1小时后重置
```

### 5. 批量操作

```python
# 批量获取用户信息
user_ids = ["user_1", "user_2", "user_3"]
keys = [f"user_info:{uid}" for uid in user_ids]
user_infos = await redis_repo.mget(keys)

# 批量设置配置
config_mapping = {
    "config:jwt_secret": "secret_key",
    "config:session_timeout": "3600"
}
await redis_repo.mset(config_mapping)
```

### 6. 性能监控

```python
# 健康检查
is_healthy = await redis_repo.ping()

# 获取内存使用情况
memory_info = await redis_repo.info("memory")
used_memory = memory_info.get("used_memory_human")

# 清理过期数据
cleaned_count = await redis_repo.delete_pattern("expired_session:*")
```

### 7. 事务操作

```python
# 原子操作示例
await redis_repo.watch("user_balance:123")
await redis_repo.multi()
await redis_repo.decr("user_balance:123", 100)
await redis_repo.incr("transaction_count")
result = await redis_repo.execute()

if result is None:
    # 事务被中断
    print("事务失败，数据被其他操作修改")
```

## 🔒 安全考虑

### 1. 键前缀
所有方法都会自动添加配置的键前缀，确保数据隔离：
```python
# 配置前缀为 "iam"
await redis_repo.set("user:123", data)  # 实际键: "iam:user:123"
```

### 2. 模式删除安全
`delete_pattern()` 方法会检查前缀，避免误删：
```python
# 只删除带前缀的键
await redis_repo.delete_pattern("session:*")  # 删除 "iam:session:*"
```

### 3. 清理操作保护
`clear_all()` 方法在没有前缀时会拒绝执行，防止误删整个数据库。

## 📊 性能特性

### 1. 字节处理
自动处理Redis返回的字节类型，统一转换为字符串：
```python
# 自动处理 b"value" -> "value"
result = await redis_repo.get("key")  # 返回字符串而不是字节
```

### 2. 批量优化
批量操作减少网络往返次数：
```python
# 一次调用获取多个值
values = await redis_repo.mget(["key1", "key2", "key3"])
```

### 3. 连接复用
所有方法复用现有的Redis连接，避免连接开销。

## 🧪 测试覆盖

新增方法都有对应的单元测试：
- 正常功能测试
- 边界条件测试
- 错误处理测试
- 字节类型处理测试

运行测试：
```bash
python -m pytest tests/test_redis_repository_extensions.py -v
```

## 📈 兼容性

### 向后兼容
- 所有原有方法保持不变
- 新方法不影响现有功能
- 保持相同的错误处理模式

### Redis版本
支持Redis 3.0+的所有版本，新方法使用的都是标准Redis命令。

## 🔄 迁移指南

如果你的代码中有自定义的Redis操作，可以迁移到新的方法：

### 迁移前
```python
# 手动实现的键查找
keys = []
cursor = 0
while True:
    cursor, batch = await redis_client.scan(cursor, match="session:*")
    keys.extend(batch)
    if cursor == 0:
        break
```

### 迁移后
```python
# 使用新的keys方法
keys = await redis_repo.keys("session:*")
```

## 📝 最佳实践

1. **使用合适的数据结构**：
   - 计数器：使用 `incr()/decr()`
   - 日志：使用列表操作 `lpush()/ltrim()`
   - 会话管理：使用集合操作 `sadd()/srem()`

2. **设置合理的TTL**：
   ```python
   await redis_repo.set("temp_data", value, ttl=3600)
   # 或者后设置
   await redis_repo.expire("temp_data", 3600)
   ```

3. **使用事务保证原子性**：
   ```python
   await redis_repo.watch("critical_key")
   await redis_repo.multi()
   # ... 多个操作
   result = await redis_repo.execute()
   ```

4. **定期清理过期数据**：
   ```python
   # 定期清理任务
   cleaned = await redis_repo.delete_pattern("expired:*")
   ```

5. **监控Redis健康状态**：
   ```python
   if not await redis_repo.ping():
       # 处理连接问题
       pass
   ```

这些扩展方法为JWT管理器和其他高级功能提供了强大的Redis操作能力，同时保持了简单易用的API设计。
