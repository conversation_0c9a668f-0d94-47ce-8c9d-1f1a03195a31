from aio_pika import Connection, connect_robust
from aio_pika import exceptions as aio_pika_exceptions
from commonlib.configs.storages.queue.rabbitmq_config import RabbitMQConfig
from commonlib.storages.base import BaseConnector


class RabbitMQConnector(BaseConnector[Connection, RabbitMQConfig]):
    """RabbitMQ 异步连接器"""

    def __init__(self) -> None:
        super().__init__()
        self._config: RabbitMQConfig | None = None

    @property
    def name(self) -> str:
        return "RabbitMQ"

    def load_config(self, config: RabbitMQConfig) -> RabbitMQConfig:
        """加载 RabbitMQ 配置

        Raises:
            ValueError: RabbitMQ 配置缺失
        """
        self._config = config
        return self._config

    async def connect(self) -> None:
        """建立 RabbitMQ 连接，带重试机制"""
        try:
            self._connection = await connect_robust(
                str(self._config.DSN), **self._config.RABBITMQ_POOL_CONFIG
            )
            self._logger.info(
                f"RabbitMQ connection established. Host: {self._config.RABBITMQ_HOST}, "
                f"VHost: {self._config.RABBITMQ_VHOST}"
            )
            return
        except Exception as e:
            self._logger.error(f"Unexpected error during RabbitMQ connection: {e}")
            raise ConnectionError(f"Unexpected error: {e}")

    async def _close(self) -> None:
        """关闭 RabbitMQ 连接，保证幂等性"""
        if not self._connection:
            self._logger.warning(
                "RabbitMQ connection is already closed or was never initialized."
            )
            return

        try:
            await self._connection.close()
            self._logger.info(
                f"RabbitMQ connection closed. Host: {self._config.RABBITMQ_HOST}, "
                f"VHost: {self._config.RABBITMQ_VHOST}"
            )
        except aio_pika_exceptions.AMQPConnectionError as e:
            self._logger.error(f"RabbitMQ connection close failed. Error: {e}")
            raise ConnectionError(f"Error closing RabbitMQ connection: {e}") from e
        except Exception as e:
            self._logger.error(
                f"Unexpected error while closing RabbitMQ connection: {e}"
            )
            raise ConnectionError(
                f"Unexpected error during RabbitMQ closing: {e}"
            ) from e
        finally:
            self._connection = None
