import time
from collections import defaultdict
from typing import Callable

from commonlib.core.logging.tsif_logging import app_logger
from fastapi import FastAPI, Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp


class MetricsMiddleware(BaseHTTPMiddleware):
    """性能监控中间件，收集请求指标"""

    def __init__(self, app: ASGIApp, log_interval: int = 60) -> None:
        """
        Args:
            app: FastAPI应用实例
            log_interval: 日志记录间隔（秒）
        """
        super().__init__(app)
        self._log_interval = log_interval
        self._last_log_time = time.time()

        # 指标存储
        self._request_count = defaultdict(int)
        self._error_count = defaultdict(int)
        self._total_duration = defaultdict(float)
        self._max_duration = defaultdict(float)

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """处理请求并收集指标

        Args:
            request: FastAPI请求对象
            call_next: 下一个处理器

        Returns:
            Response: FastAPI响应对象
        """
        path = request.url.path
        method = request.method
        start_time = time.time()

        try:
            response = await call_next(request)

            # 记录请求时间
            duration = time.time() - start_time
            self._update_metrics(path, method, duration, response.status_code < 400)

            # 添加性能指标到响应头
            response.headers["X-Response-Time"] = str(round(duration * 1000, 2))
            return response

        except Exception:
            # 记录错误
            duration = time.time() - start_time
            self._update_metrics(path, method, duration, False)
            raise

        finally:
            # 定期记录指标
            self._log_metrics_if_needed()

    def _update_metrics(
        self, path: str, method: str, duration: float, success: bool
    ) -> None:
        """更新性能指标

        Args:
            path: 请求路径
            method: HTTP方法
            duration: 请求处理时间
            success: 是否成功
        """
        key = f"{method} {path}"
        self._request_count[key] += 1
        self._total_duration[key] += duration
        self._max_duration[key] = max(self._max_duration[key], duration)

        if not success:
            self._error_count[key] += 1

    def _log_metrics_if_needed(self) -> None:
        """定期记录性能指标"""
        now = time.time()
        if now - self._last_log_time >= self._log_interval:
            self._log_metrics()
            self._reset_metrics()
            self._last_log_time = now

    def _log_metrics(self) -> None:
        """记录性能指标"""
        for key in self._request_count:
            count = self._request_count[key]
            if count > 0:
                avg_duration = self._total_duration[key] / count
                error_rate = (self._error_count[key] / count) * 100

                app_logger.info(
                    f"Performance metrics for {key}",
                    extra={
                        "endpoint": key,
                        "request_count": count,
                        "avg_duration_ms": round(avg_duration * 1000, 2),
                        "max_duration_ms": round(self._max_duration[key] * 1000, 2),
                        "error_rate": round(error_rate, 2),
                        "error_count": self._error_count[key],
                        "interval_seconds": self._log_interval,
                    },
                )

    def _reset_metrics(self) -> None:
        """重置性能指标"""
        self._request_count.clear()
        self._error_count.clear()
        self._total_duration.clear()
        self._max_duration.clear()


def setup_metrics_middleware(app: FastAPI, log_interval: int = 60) -> None:
    """配置性能监控中间件

    Args:
        app: FastAPI应用实例
        log_interval: 日志记录间隔（秒）
    """

    app.add_middleware(MetricsMiddleware, log_interval=log_interval)

    app_logger.info(
        "Metrics middleware configured", extra={"log_interval": log_interval}
    )
