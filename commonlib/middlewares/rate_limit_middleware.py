import time
from typing import Callable, Dict

from commonlib.core.logging.tsif_logging import app_logger
from fastapi import FastAPI, HTTPException, Request
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response
from starlette.types import ASGIApp


class TokenBucket:
    """令牌桶算法实现"""

    def __init__(self, rate: float, capacity: int) -> None:
        """
        Args:
            rate: 令牌生成速率（每秒）
            capacity: 桶容量
        """
        self.rate = rate
        self.capacity = capacity
        self.tokens = capacity
        self.last_update = time.time()

    def _add_tokens(self) -> None:
        """添加令牌"""
        now = time.time()
        time_passed = now - self.last_update
        new_tokens = time_passed * self.rate

        self.tokens = min(self.capacity, self.tokens + new_tokens)
        self.last_update = now

    def try_consume(self, tokens: int = 1) -> bool:
        """尝试消费令牌

        Args:
            tokens: 需要消费的令牌数

        Returns:
            bool: 是否成功消费
        """
        self._add_tokens()
        if self.tokens >= tokens:
            self.tokens -= tokens
            return True
        return False


class RateLimitMiddleware(BaseHTTPMiddleware):
    """请求限流中间件，使用令牌桶算法"""

    def __init__(
        self,
        app: ASGIApp,
        rate: float = 10.0,
        capacity: int = 100,
        window_size: int = 3600,
        by_ip: bool = True,
    ) -> None:
        """
        Args:
            app: FastAPI应用实例
            rate: 请求速率（每秒）
            capacity: 桶容量
            window_size: 时间窗口大小（秒）
            by_ip: 是否按IP限流
        """
        super().__init__(app)
        self.rate = rate
        self.capacity = capacity
        self.window_size = window_size
        self.by_ip = by_ip
        self.buckets: Dict[str, TokenBucket] = {}
        self.last_cleanup = time.time()

    def _get_bucket_key(self, request: Request) -> str:
        """获取限流桶的键

        Args:
            request: FastAPI请求对象

        Returns:
            str: 限流桶键
        """
        if self.by_ip:
            return request.client.host
        return request.url.path

    def _cleanup_old_buckets(self) -> None:
        """清理过期的限流桶"""
        now = time.time()
        if now - self.last_cleanup > self.window_size:
            self.buckets.clear()
            self.last_cleanup = now

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """处理请求并进行限流

        Args:
            request: FastAPI请求对象
            call_next: 下一个处理器

        Returns:
            Response: FastAPI响应对象

        Raises:
            HTTPException: 请求被限流时抛出 429 错误
        """
        self._cleanup_old_buckets()

        bucket_key = self._get_bucket_key(request)
        if bucket_key not in self.buckets:
            self.buckets[bucket_key] = TokenBucket(self.rate, self.capacity)

        if not self.buckets[bucket_key].try_consume():
            app_logger.warning(
                "Rate limit exceeded",
                extra={
                    "client_ip": request.client.host,
                    "path": request.url.path,
                    "bucket_key": bucket_key,
                },
            )
            raise HTTPException(status_code=429, detail="Too many requests")

        return await call_next(request)


def setup_rate_limit_middleware(
    app: FastAPI,
    rate: float = 10.0,
    capacity: int = 100,
    window_size: int = 3600,
    by_ip: bool = True,
) -> None:
    """配置请求限流中间件

    Args:
        app: FastAPI应用实例
        rate: 请求速率（每秒）
        capacity: 桶容量
        window_size: 时间窗口大小（秒）
        by_ip: 是否按IP限流
    """

    app.add_middleware(
        RateLimitMiddleware,
        rate=rate,
        capacity=capacity,
        window_size=window_size,
        by_ip=by_ip,
    )

    app_logger.info(
        "Rate limit middleware configured",
        extra={
            "rate": rate,
            "capacity": capacity,
            "window_size": window_size,
            "by_ip": by_ip,
        },
    )
