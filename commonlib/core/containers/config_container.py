from pathlib import Path
from typing import List, Optional, Union

from commonlib.configs.base_setting import AppSettings
from commonlib.configs.config_loader import ConfigLoader
from commonlib.core.logging.tsif_logging import app_logger
from dependency_injector import containers, providers
from dotenv import load_dotenv


class ConfigContainer(containers.DeclarativeContainer):
    """所有微服务共享的根配置容器"""

    # 加载环境变量
    load_dotenv()

    # 配置加载器（Singleton 确保全局唯一）
    config_loader = providers.Singleton(ConfigLoader)

    config = providers.Resource(lambda c: c.get_config(), config_loader)

    # 日志资源（依赖 configs）
    logger = providers.Resource(
        app_logger.initialize,
        log_dir=config.provided.log_dir,
        app_name=providers.Callable(lambda c: c.application.project_name, config),
        debug=config.provided.debug,
    )

    # 暴露常用配置项
    persistence_config = providers.Callable(lambda c: c.persistence, config)

    connection_priority_config = providers.Callable(
        lambda c: c.connection_priority, config
    )
    redis_config = providers.Callable(lambda c: c.persistence.redis, config)
    mysql_config = providers.Callable(lambda c: c.persistence.mysql, config)


def set_up_config_di(
    app_setting: AppSettings = None,
    config_path: Optional[Union[str, Path]] = None,
    additional_paths: Optional[List[Union[str, Path]]] = None,
) -> ConfigContainer:
    """初始化依赖注入容器"""
    if not app_setting:
        app_setting = AppSettings()
    container = ConfigContainer()

    # 显式加载配置（可选：支持传入自定义路径）
    container.config_loader().load(app_setting, config_path, additional_paths)

    # 初始化资源（如日志、数据库连接等）
    container.init_resources()
    print(container.config())
    return container


if __name__ == "__main__":
    # 初始化容器（可传入自定义配置路径）
    ts_config_container = set_up_config_di(AppSettings())

    # 测试配置访问
    print(ts_config_container.config())
    print(ts_config_container.config().log_dir)
    print(ts_config_container.redis_config())

    print(ts_config_container.mysql_config())
    print(ts_config_container.connection_priority_config())

    # 测试日志
    app_logger.info("Configuration loaded successfully!")
