from typing import Any

from pydantic import AmqpDsn, BaseModel, ConfigDict, Field, NonNegativeInt


class RabbitMQConfig(BaseModel):
    """RabbitMQ服务配置"""

    # 基础连接配置
    RABBITMQ_HOST: str = Field(
        default="localhost",
        description="RabbitMQ地址",
    )
    RABBITMQ_PORT: int = Field(
        default=5672, description="RabbitMQ端口", ge=1024, le=65535
    )
    RABBITMQ_USERNAME: str = Field(
        default="guest",
        description="RabbitMQ用户名",
        min_length=1,
        max_length=32,
    )
    RABBITMQ_PASSWORD: str = Field(
        default="guest",
        description="RabbitMQ密码",
        min_length=1,
    )
    RABBITMQ_VHOST: str = Field(
        default="/",
        description="RabbitMQ虚拟主机",
    )
    RABBITMQ_SCHEME: str = Field(
        default="amqp", description="RabbitMQ连接协议", pattern="^amqps?$"
    )

    # 连接池配置
    RABBITMQ_POOL_SIZE: NonNegativeInt = Field(
        default=10, description="连接池大小", ge=1, le=1000
    )
    RABBITMQ_POOL_TIMEOUT: NonNegativeInt = Field(
        default=30, description="连接超时时间(秒)", ge=1, le=300
    )
    RABBITMQ_POOL_RECYCLE: NonNegativeInt = Field(
        default=3600, description="连接回收时间(秒)", ge=300, le=86400
    )
    RABBITMQ_HEARTBEAT: NonNegativeInt = Field(
        default=60, description="心跳间隔(秒)", ge=10, le=600
    )
    RABBITMQ_SSL: bool = Field(default=False, description="是否使用SSL连接")

    @property
    def DSN(self) -> AmqpDsn:
        """构造RabbitMQ DSN连接字符串"""
        scheme = "amqps" if self.RABBITMQ_SSL else "amqp"
        return AmqpDsn.build(
            scheme=scheme,
            username=self.RABBITMQ_USERNAME,
            password=self.RABBITMQ_PASSWORD,
            host=self.RABBITMQ_HOST,
            port=self.RABBITMQ_PORT,
            path=self.RABBITMQ_VHOST,
        )

    @property
    def RABBITMQ_POOL_CONFIG(self) -> dict[str, Any]:
        """连接池配置"""
        return {
            "pool_size": self.RABBITMQ_POOL_SIZE,
            "pool_timeout": self.RABBITMQ_POOL_TIMEOUT,
            "pool_recycle": self.RABBITMQ_POOL_RECYCLE,
            "heartbeat": self.RABBITMQ_HEARTBEAT,
            "ssl": self.RABBITMQ_SSL,
        }

    model_config = ConfigDict(
        validate_assignment=True,
        validate_default=True,
        json_encoders={AmqpDsn: str},
        extra="allow",
    )
