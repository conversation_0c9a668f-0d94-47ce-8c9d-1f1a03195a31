from typing import Any, Dict

from arq.connections import RedisSettings as ArqRedisSettings
from pydantic import BaseModel, ConfigDict, Field, NonNegativeInt, RedisDsn


class RedisConfig(BaseModel):
    """Redis 服务配置"""

    # 基础连接配置
    REDIS_HOST: str = Field(default="localhost", description="Redis 地址")
    REDIS_PORT: int = Field(default=6379, description="Redis 端口", ge=1024, le=65535)
    REDIS_DB: int = Field(default=0, description="Redis 数据库索引", ge=0, le=15)
    REDIS_USERNAME: str | None = Field(default="", description="Redis 用户名")
    REDIS_PASSWORD: str | None = Field(default=None, description="Redis 密码")
    REDIS_POOL_SIZE: NonNegativeInt = Field(
        default=10, description="连接池大小（仅用于记录）", ge=1, le=1000
    )
    REDIS_MAX_CONNECTIONS: NonNegativeInt = Field(
        default=10, description="Redis 最大连接数", ge=1, le=60
    )
    REDIS_POOL_TIMEOUT: NonNegativeInt = Field(
        default=5, description="连接超时时间（秒）", ge=1, le=60
    )
    REDIS_POOL_RECYCLE: NonNegativeInt = Field(
        default=3600, description="连接回收时间（秒）", ge=300, le=86400
    )
    REDIS_RETRY_ON_TIMEOUT: bool = Field(default=True, description="超时是否重试")
    REDIS_POOL_PRE_PING: bool = Field(default=True, description="是否启用连接预检测")
    REDIS_DECODE_RESPONSE: bool = Field(
        default=True, description="是否自动将返回值解码为字符串"
    )

    @property
    def DSN(self) -> RedisDsn:
        """构造 Redis DSN 连接字符串"""
        scheme = "redis"
        return RedisDsn.build(
            scheme=scheme,
            host=self.REDIS_HOST,
            port=self.REDIS_PORT,
            path=f"/{self.REDIS_DB}",
            password=self.REDIS_PASSWORD,
        )

    @property
    def REDIS_POOL_CONFIG(self) -> Dict[str, Any]:
        return {
            "max_connections": self.REDIS_MAX_CONNECTIONS,
            "socket_timeout": self.REDIS_POOL_TIMEOUT,
            "socket_connect_timeout": self.REDIS_POOL_TIMEOUT,
            "health_check_interval": self.REDIS_POOL_RECYCLE,
            "retry_on_timeout": self.REDIS_RETRY_ON_TIMEOUT,
            "socket_keepalive": self.REDIS_POOL_PRE_PING,
            "decode_responses": self.REDIS_DECODE_RESPONSE,
        }

    def build_arq_redis_setting(self) -> ArqRedisSettings:
        return ArqRedisSettings(
            host=self.REDIS_HOST,
            port=self.REDIS_PORT,
            database=self.REDIS_DB,
            username=self.REDIS_USERNAME,
            password=self.REDIS_PASSWORD,
        )

    model_config = ConfigDict(
        validate_assignment=True,
        validate_default=True,
        json_encoders={RedisDsn: str},
        extra="allow",
    )
