from pydantic import BaseModel


class FastApiSettings(BaseModel):
    # 基础配置
    debug: bool = False
    title: str = "Default App"
    project_name: str = "My Project"
    description: str | None = "FastAPI Application"
    secret_key: str = "default-insecure-secret-key"

    # API 文档配置
    docs_url: str | None = "/docs"
    openapi_url: str | None = "/openapi.json"
    redoc_url: str | None = "/redoc"
    log_dir: str | None = ""
