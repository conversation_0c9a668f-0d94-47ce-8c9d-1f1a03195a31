"""
RedisKeyBuilder - 统一Redis Key命名规范

提供统一的Redis键命名规范和构建方法，确保键的一致性和可维护性
"""

import hashlib
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum


class KeyType(Enum):
    """键类型枚举"""
    CACHE = "cache"           # 缓存数据
    SESSION = "session"       # 会话数据
    LOCK = "lock"            # 分布式锁
    QUEUE = "queue"          # 队列数据
    COUNTER = "counter"      # 计数器
    SET = "set"              # 集合数据
    HASH = "hash"            # 哈希数据
    TEMP = "temp"            # 临时数据
    CONFIG = "config"        # 配置数据
    TOKEN = "token"          # 令牌数据


class RedisKeyBuilder:
    """Redis键构建器"""
    
    def __init__(self, app_name: str = "iam", environment: str = "prod"):
        """
        初始化键构建器
        
        Args:
            app_name: 应用名称
            environment: 环境标识（dev/test/prod）
        """
        self.app_name = app_name
        self.environment = environment
        self.separator = ":"
    
    def build_key(
        self,
        key_type: KeyType,
        module: str,
        identifier: str,
        sub_key: Optional[str] = None,
        tenant_id: Optional[str] = None,
        version: Optional[str] = None
    ) -> str:
        """
        构建标准化的Redis键
        
        格式: {app_name}:{env}:{key_type}:{module}:{tenant_id}:{identifier}:{sub_key}:{version}
        
        Args:
            key_type: 键类型
            module: 模块名称（如：user, tenant, auth等）
            identifier: 主要标识符（如：user_id, tenant_id等）
            sub_key: 子键（可选）
            tenant_id: 租户ID（可选，多租户场景）
            version: 版本号（可选）
            
        Returns:
            构建的Redis键
        """
        parts = [self.app_name, self.environment, key_type.value, module]
        
        # 添加租户ID（如果提供）
        if tenant_id:
            parts.append(f"tenant_{tenant_id}")
            
        # 添加主标识符
        parts.append(identifier)
        
        # 添加子键（如果提供）
        if sub_key:
            parts.append(sub_key)
            
        # 添加版本号（如果提供）
        if version:
            parts.append(f"v{version}")
            
        return self.separator.join(parts)
    
    def build_pattern(
        self,
        key_type: KeyType,
        module: str,
        pattern: str = "*",
        tenant_id: Optional[str] = None
    ) -> str:
        """
        构建键匹配模式
        
        Args:
            key_type: 键类型
            module: 模块名称
            pattern: 匹配模式
            tenant_id: 租户ID（可选）
            
        Returns:
            键匹配模式
        """
        parts = [self.app_name, self.environment, key_type.value, module]
        
        if tenant_id:
            parts.append(f"tenant_{tenant_id}")
            
        parts.append(pattern)
        
        return self.separator.join(parts)
    
    # ==================== 用户相关键 ====================
    
    def user_info_key(self, user_id: str, tenant_id: Optional[str] = None) -> str:
        """用户基本信息缓存键"""
        return self.build_key(KeyType.CACHE, "user", user_id, "info", tenant_id)
    
    def user_roles_key(self, user_id: str, tenant_id: str) -> str:
        """用户角色缓存键"""
        return self.build_key(KeyType.CACHE, "user", user_id, "roles", tenant_id)
    
    def user_permissions_key(self, user_id: str, tenant_id: str) -> str:
        """用户权限缓存键"""
        return self.build_key(KeyType.CACHE, "user", user_id, "permissions", tenant_id)
    
    def user_session_key(self, session_id: str, tenant_id: Optional[str] = None) -> str:
        """用户会话键"""
        return self.build_key(KeyType.SESSION, "user", session_id, tenant_id=tenant_id)
    
    def user_login_attempts_key(self, user_id: str, tenant_id: Optional[str] = None) -> str:
        """用户登录尝试计数键"""
        return self.build_key(KeyType.COUNTER, "user", user_id, "login_attempts", tenant_id)
    
    # ==================== 租户相关键 ====================
    
    def tenant_info_key(self, tenant_id: str) -> str:
        """租户信息缓存键"""
        return self.build_key(KeyType.CACHE, "tenant", tenant_id, "info")
    
    def tenant_config_key(self, tenant_id: str) -> str:
        """租户配置缓存键"""
        return self.build_key(KeyType.CONFIG, "tenant", tenant_id, "config")
    
    def tenant_users_key(self, tenant_id: str) -> str:
        """租户用户列表键"""
        return self.build_key(KeyType.SET, "tenant", tenant_id, "users")
    
    # ==================== 认证相关键 ====================
    
    def jwt_token_key(self, token_id: str, tenant_id: Optional[str] = None) -> str:
        """JWT令牌键"""
        return self.build_key(KeyType.TOKEN, "auth", token_id, "jwt", tenant_id)
    
    def refresh_token_key(self, token_id: str, tenant_id: Optional[str] = None) -> str:
        """刷新令牌键"""
        return self.build_key(KeyType.TOKEN, "auth", token_id, "refresh", tenant_id)
    
    def auth_code_key(self, code: str, tenant_id: Optional[str] = None) -> str:
        """认证码键"""
        return self.build_key(KeyType.TEMP, "auth", code, "code", tenant_id)
    
    # ==================== 权限相关键 ====================
    
    def permission_check_key(
        self,
        user_id: str,
        resource: str,
        action: str,
        tenant_id: str
    ) -> str:
        """权限检查结果缓存键"""
        # 使用哈希避免键过长
        check_str = f"{user_id}:{tenant_id}:{resource}:{action}"
        check_hash = hashlib.md5(check_str.encode()).hexdigest()[:16]
        return self.build_key(KeyType.CACHE, "permission", check_hash, "check", tenant_id)
    
    def role_permissions_key(self, role_id: str, tenant_id: str) -> str:
        """角色权限缓存键"""
        return self.build_key(KeyType.CACHE, "role", role_id, "permissions", tenant_id)
    
    # ==================== 锁相关键 ====================
    
    def user_operation_lock_key(self, user_id: str, operation: str, tenant_id: Optional[str] = None) -> str:
        """用户操作锁键"""
        return self.build_key(KeyType.LOCK, "user", user_id, operation, tenant_id)
    
    def tenant_operation_lock_key(self, tenant_id: str, operation: str) -> str:
        """租户操作锁键"""
        return self.build_key(KeyType.LOCK, "tenant", tenant_id, operation)
    
    def global_operation_lock_key(self, operation: str) -> str:
        """全局操作锁键"""
        return self.build_key(KeyType.LOCK, "global", "system", operation)
    
    # ==================== 队列相关键 ====================
    
    def notification_queue_key(self, tenant_id: Optional[str] = None) -> str:
        """通知队列键"""
        return self.build_key(KeyType.QUEUE, "notification", "messages", tenant_id=tenant_id)
    
    def audit_queue_key(self, tenant_id: Optional[str] = None) -> str:
        """审计队列键"""
        return self.build_key(KeyType.QUEUE, "audit", "logs", tenant_id=tenant_id)
    
    def task_queue_key(self, queue_name: str, tenant_id: Optional[str] = None) -> str:
        """任务队列键"""
        return self.build_key(KeyType.QUEUE, "task", queue_name, tenant_id=tenant_id)
    
    # ==================== 统计相关键 ====================
    
    def daily_stats_key(self, date: str, metric: str, tenant_id: Optional[str] = None) -> str:
        """日统计键"""
        return self.build_key(KeyType.COUNTER, "stats", date, metric, tenant_id)
    
    def hourly_stats_key(self, hour: str, metric: str, tenant_id: Optional[str] = None) -> str:
        """小时统计键"""
        return self.build_key(KeyType.COUNTER, "stats", hour, metric, tenant_id)
    
    # ==================== 工具方法 ====================
    
    def extract_parts(self, key: str) -> Dict[str, str]:
        """
        从键中提取各部分信息
        
        Args:
            key: Redis键
            
        Returns:
            包含各部分信息的字典
        """
        parts = key.split(self.separator)
        if len(parts) < 4:
            return {}
            
        result = {
            "app_name": parts[0],
            "environment": parts[1],
            "key_type": parts[2],
            "module": parts[3]
        }
        
        # 解析剩余部分
        remaining = parts[4:]
        if remaining:
            # 检查是否有租户ID
            if remaining[0].startswith("tenant_"):
                result["tenant_id"] = remaining[0][7:]  # 移除"tenant_"前缀
                remaining = remaining[1:]
            
            if remaining:
                result["identifier"] = remaining[0]
                remaining = remaining[1:]
                
            if remaining:
                result["sub_key"] = remaining[0]
                remaining = remaining[1:]
                
            if remaining and remaining[0].startswith("v"):
                result["version"] = remaining[0][1:]  # 移除"v"前缀
        
        return result
    
    def is_expired_key(self, key: str, current_time: datetime) -> bool:
        """
        检查键是否应该过期（基于键名中的时间信息）
        
        Args:
            key: Redis键
            current_time: 当前时间
            
        Returns:
            是否应该过期
        """
        parts = self.extract_parts(key)
        
        # 检查是否是临时键
        if parts.get("key_type") == KeyType.TEMP.value:
            return True
            
        # 检查是否是基于时间的统计键
        if parts.get("module") == "stats":
            identifier = parts.get("identifier", "")
            try:
                # 尝试解析日期格式的标识符
                if len(identifier) == 10:  # YYYY-MM-DD
                    key_date = datetime.strptime(identifier, "%Y-%m-%d")
                    # 保留7天的统计数据
                    return (current_time - key_date).days > 7
                elif len(identifier) == 13:  # YYYY-MM-DD-HH
                    key_hour = datetime.strptime(identifier, "%Y-%m-%d-%H")
                    # 保留24小时的统计数据
                    return (current_time - key_hour).total_seconds() > 86400
            except ValueError:
                pass
                
        return False
