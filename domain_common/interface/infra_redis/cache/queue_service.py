"""
RedisQueueService - Redis队列服务封装

提供基于Redis的队列操作，包括FIFO队列、优先级队列、延迟队列等功能
"""

import json
import asyncio
from typing import Any, Dict, List, Optional, Union, Callable, TypeVar
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
import uuid

from commonlib.storages.persistence.redis.repository import RedisRepository
from .key_builder import RedisKeyBuilder, KeyType

T = TypeVar('T')


class QueueType(Enum):
    """队列类型"""
    FIFO = "fifo"           # 先进先出队列
    LIFO = "lifo"           # 后进先出队列（栈）
    PRIORITY = "priority"   # 优先级队列
    DELAY = "delay"         # 延迟队列


@dataclass
class QueueMessage:
    """队列消息"""
    id: str
    payload: Any
    created_at: datetime
    priority: int = 0
    delay_until: Optional[datetime] = None
    retry_count: int = 0
    max_retries: int = 3
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class QueueConfig:
    """队列配置"""
    max_size: int = 10000           # 队列最大大小
    message_ttl: int = 86400        # 消息TTL（秒）
    retry_delay: int = 60           # 重试延迟（秒）
    dead_letter_enabled: bool = True # 是否启用死信队列
    batch_size: int = 10            # 批量处理大小


class RedisQueueService:
    """Redis队列服务"""
    
    def __init__(
        self,
        redis_repo: RedisRepository,
        key_builder: RedisKeyBuilder,
        config: Optional[QueueConfig] = None
    ):
        """
        初始化队列服务
        
        Args:
            redis_repo: Redis仓库实例
            key_builder: 键构建器实例
            config: 队列配置
        """
        self.redis_repo = redis_repo
        self.key_builder = key_builder
        self.config = config or QueueConfig()
    
    # ==================== 基础队列操作 ====================
    
    async def push(
        self,
        queue_name: str,
        payload: T,
        priority: int = 0,
        delay: Optional[timedelta] = None,
        metadata: Optional[Dict[str, Any]] = None,
        tenant_id: Optional[str] = None
    ) -> str:
        """
        推送消息到队列
        
        Args:
            queue_name: 队列名称
            payload: 消息载荷
            priority: 优先级（数字越大优先级越高）
            delay: 延迟时间
            metadata: 元数据
            tenant_id: 租户ID
            
        Returns:
            消息ID
        """
        try:
            # 生成消息ID
            message_id = str(uuid.uuid4())
            
            # 创建消息对象
            message = QueueMessage(
                id=message_id,
                payload=payload,
                created_at=datetime.now(),
                priority=priority,
                delay_until=datetime.now() + delay if delay else None,
                metadata=metadata
            )
            
            # 序列化消息
            serialized_message = json.dumps(asdict(message), default=self._json_serializer)
            
            # 根据是否有延迟选择不同的队列
            if delay:
                # 延迟队列：使用有序集合，分数为执行时间戳
                delay_key = self.key_builder.build_key(
                    KeyType.QUEUE, "delay", queue_name, tenant_id=tenant_id
                )
                score = message.delay_until.timestamp()
                await self.redis_repo.zadd(delay_key, {serialized_message: score})
            else:
                # 立即队列：根据优先级选择操作
                queue_key = self.key_builder.build_key(
                    KeyType.QUEUE, "main", queue_name, tenant_id=tenant_id
                )
                
                if priority > 0:
                    # 优先级队列：使用有序集合
                    await self.redis_repo.zadd(queue_key, {serialized_message: priority})
                else:
                    # 普通FIFO队列：使用列表
                    await self.redis_repo.lpush(queue_key, serialized_message)
            
            # 设置队列TTL
            await self._set_queue_ttl(queue_name, tenant_id)
            
            return message_id
            
        except Exception as e:
            print(f"Queue push error for queue {queue_name}: {e}")
            raise
    
    async def pop(
        self,
        queue_name: str,
        timeout: int = 0,
        tenant_id: Optional[str] = None
    ) -> Optional[QueueMessage]:
        """
        从队列弹出消息
        
        Args:
            queue_name: 队列名称
            timeout: 阻塞超时时间（秒），0表示非阻塞
            tenant_id: 租户ID
            
        Returns:
            队列消息或None
        """
        try:
            # 首先检查延迟队列中是否有到期的消息
            await self._process_delayed_messages(queue_name, tenant_id)
            
            queue_key = self.key_builder.build_key(
                KeyType.QUEUE, "main", queue_name, tenant_id=tenant_id
            )
            
            # 尝试从优先级队列弹出（有序集合）
            priority_result = await self.redis_repo.client.zpopmax(queue_key)
            if priority_result:
                serialized_message = priority_result[0][0]
                return self._deserialize_message(serialized_message)
            
            # 尝试从普通队列弹出（列表）
            if timeout > 0:
                # 阻塞弹出
                result = await self.redis_repo.client.brpop([queue_key], timeout)
                if result:
                    serialized_message = result[1]
                    return self._deserialize_message(serialized_message)
            else:
                # 非阻塞弹出
                serialized_message = await self.redis_repo.rpop(queue_key)
                if serialized_message:
                    return self._deserialize_message(serialized_message)
            
            return None
            
        except Exception as e:
            print(f"Queue pop error for queue {queue_name}: {e}")
            return None
    
    async def peek(
        self,
        queue_name: str,
        count: int = 1,
        tenant_id: Optional[str] = None
    ) -> List[QueueMessage]:
        """
        查看队列中的消息（不弹出）
        
        Args:
            queue_name: 队列名称
            count: 查看数量
            tenant_id: 租户ID
            
        Returns:
            消息列表
        """
        try:
            queue_key = self.key_builder.build_key(
                KeyType.QUEUE, "main", queue_name, tenant_id=tenant_id
            )
            
            messages = []
            
            # 查看优先级队列
            priority_messages = await self.redis_repo.client.zrevrange(queue_key, 0, count - 1, withscores=True)
            for msg_data in priority_messages:
                message = self._deserialize_message(msg_data[0])
                if message:
                    messages.append(message)
            
            # 如果优先级队列消息不够，查看普通队列
            if len(messages) < count:
                remaining = count - len(messages)
                list_messages = await self.redis_repo.lrange(queue_key, -remaining, -1)
                for serialized_message in reversed(list_messages):
                    message = self._deserialize_message(serialized_message)
                    if message:
                        messages.append(message)
            
            return messages[:count]
            
        except Exception as e:
            print(f"Queue peek error for queue {queue_name}: {e}")
            return []
    
    async def size(self, queue_name: str, tenant_id: Optional[str] = None) -> int:
        """
        获取队列大小
        
        Args:
            queue_name: 队列名称
            tenant_id: 租户ID
            
        Returns:
            队列大小
        """
        try:
            queue_key = self.key_builder.build_key(
                KeyType.QUEUE, "main", queue_name, tenant_id=tenant_id
            )
            
            # 获取优先级队列大小
            priority_size = await self.redis_repo.client.zcard(queue_key)
            
            # 获取普通队列大小
            list_size = await self.redis_repo.llen(queue_key)
            
            return priority_size + list_size
            
        except Exception as e:
            print(f"Queue size error for queue {queue_name}: {e}")
            return 0
    
    async def clear(self, queue_name: str, tenant_id: Optional[str] = None) -> bool:
        """
        清空队列
        
        Args:
            queue_name: 队列名称
            tenant_id: 租户ID
            
        Returns:
            是否成功
        """
        try:
            # 删除主队列
            main_key = self.key_builder.build_key(
                KeyType.QUEUE, "main", queue_name, tenant_id=tenant_id
            )
            
            # 删除延迟队列
            delay_key = self.key_builder.build_key(
                KeyType.QUEUE, "delay", queue_name, tenant_id=tenant_id
            )
            
            # 删除处理中队列
            processing_key = self.key_builder.build_key(
                KeyType.QUEUE, "processing", queue_name, tenant_id=tenant_id
            )
            
            await asyncio.gather(
                self.redis_repo.delete(main_key),
                self.redis_repo.delete(delay_key),
                self.redis_repo.delete(processing_key),
                return_exceptions=True
            )
            
            return True
            
        except Exception as e:
            print(f"Queue clear error for queue {queue_name}: {e}")
            return False
    
    # ==================== 批量操作 ====================
    
    async def push_batch(
        self,
        queue_name: str,
        payloads: List[T],
        priority: int = 0,
        tenant_id: Optional[str] = None
    ) -> List[str]:
        """
        批量推送消息
        
        Args:
            queue_name: 队列名称
            payloads: 消息载荷列表
            priority: 优先级
            tenant_id: 租户ID
            
        Returns:
            消息ID列表
        """
        try:
            message_ids = []
            
            # 批量创建消息
            messages = []
            for payload in payloads:
                message_id = str(uuid.uuid4())
                message = QueueMessage(
                    id=message_id,
                    payload=payload,
                    created_at=datetime.now(),
                    priority=priority
                )
                messages.append(message)
                message_ids.append(message_id)
            
            # 序列化消息
            serialized_messages = [
                json.dumps(asdict(msg), default=self._json_serializer)
                for msg in messages
            ]
            
            queue_key = self.key_builder.build_key(
                KeyType.QUEUE, "main", queue_name, tenant_id=tenant_id
            )
            
            if priority > 0:
                # 批量添加到优先级队列
                mapping = {msg: priority for msg in serialized_messages}
                await self.redis_repo.zadd(queue_key, mapping)
            else:
                # 批量添加到普通队列
                await self.redis_repo.lpush(queue_key, *serialized_messages)
            
            return message_ids
            
        except Exception as e:
            print(f"Queue push_batch error for queue {queue_name}: {e}")
            return []
    
    async def pop_batch(
        self,
        queue_name: str,
        count: int,
        tenant_id: Optional[str] = None
    ) -> List[QueueMessage]:
        """
        批量弹出消息
        
        Args:
            queue_name: 队列名称
            count: 弹出数量
            tenant_id: 租户ID
            
        Returns:
            消息列表
        """
        try:
            messages = []
            
            for _ in range(count):
                message = await self.pop(queue_name, timeout=0, tenant_id=tenant_id)
                if message:
                    messages.append(message)
                else:
                    break
            
            return messages
            
        except Exception as e:
            print(f"Queue pop_batch error for queue {queue_name}: {e}")
            return []
    
    # ==================== 延迟队列处理 ====================
    
    async def _process_delayed_messages(self, queue_name: str, tenant_id: Optional[str] = None):
        """处理延迟队列中到期的消息"""
        try:
            delay_key = self.key_builder.build_key(
                KeyType.QUEUE, "delay", queue_name, tenant_id=tenant_id
            )
            
            current_time = datetime.now().timestamp()
            
            # 获取到期的消息
            expired_messages = await self.redis_repo.client.zrangebyscore(
                delay_key, 0, current_time, withscores=True
            )
            
            if expired_messages:
                main_key = self.key_builder.build_key(
                    KeyType.QUEUE, "main", queue_name, tenant_id=tenant_id
                )
                
                # 移动到主队列
                for msg_data in expired_messages:
                    serialized_message = msg_data[0]
                    
                    # 反序列化消息以获取优先级
                    message = self._deserialize_message(serialized_message)
                    if message and message.priority > 0:
                        # 添加到优先级队列
                        await self.redis_repo.zadd(main_key, {serialized_message: message.priority})
                    else:
                        # 添加到普通队列
                        await self.redis_repo.lpush(main_key, serialized_message)
                
                # 从延迟队列中移除
                message_keys = [msg_data[0] for msg_data in expired_messages]
                await self.redis_repo.zrem(delay_key, *message_keys)
                
        except Exception as e:
            print(f"Process delayed messages error for queue {queue_name}: {e}")
    
    # ==================== 工具方法 ====================
    
    async def _set_queue_ttl(self, queue_name: str, tenant_id: Optional[str] = None):
        """设置队列TTL"""
        try:
            keys = [
                self.key_builder.build_key(KeyType.QUEUE, "main", queue_name, tenant_id=tenant_id),
                self.key_builder.build_key(KeyType.QUEUE, "delay", queue_name, tenant_id=tenant_id),
                self.key_builder.build_key(KeyType.QUEUE, "processing", queue_name, tenant_id=tenant_id)
            ]
            
            tasks = [
                self.redis_repo.expire(key, self.config.message_ttl)
                for key in keys
            ]
            await asyncio.gather(*tasks, return_exceptions=True)
            
        except Exception as e:
            print(f"Set queue TTL error for queue {queue_name}: {e}")
    
    def _deserialize_message(self, serialized_message: str) -> Optional[QueueMessage]:
        """反序列化消息"""
        try:
            data = json.loads(serialized_message, object_hook=self._json_deserializer)
            if isinstance(data, dict):
                return QueueMessage(**data)
            return None
        except Exception as e:
            print(f"Message deserialization error: {e}")
            return None
    
    def _json_serializer(self, obj: Any) -> Any:
        """JSON序列化器"""
        if isinstance(obj, datetime):
            return {"__datetime__": obj.isoformat()}
        return obj
    
    def _json_deserializer(self, obj: Dict[str, Any]) -> Any:
        """JSON反序列化器"""
        if "__datetime__" in obj:
            return datetime.fromisoformat(obj["__datetime__"])
        return obj
