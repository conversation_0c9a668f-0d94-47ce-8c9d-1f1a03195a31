import asyncio
import time
from dataclasses import dataclass
from functools import wraps
from typing import Callable, TypeVar, Union

from commonlib.core.logging.tsif_logging import app_logger
from commonlib.storages.persistence.redis.repository import RedisRepository
from commonlib.utils.func_toolkit import fullname
from domain_common.interface.infra_redis.scripts.context import \
    SCRIPT_TOKEN_BUCKET
from domain_common.interface.infra_redis.scripts.script import \
    RedisScriptManager

T = TypeVar("T")


@dataclass
class TokenBucketConfig:
    """令牌桶配置项"""

    capacity: int  # 桶容量（最大突发请求量）
    fill_rate: float  # 每秒填充速率（令牌/秒）
    key_prefix: str = "token_bucket"  # Redis键前缀
    timeout: int = 10  # 获取令牌等待超时时间（秒）
    error_message: str = "API rate limit exceeded"  # 错误信息


class TokenBucketDecorator:
    """令牌桶算法限流装饰器（适合爬虫场景）"""

    _script_key = SCRIPT_TOKEN_BUCKET

    def __init__(self, script_manager: RedisScriptManager):
        self._repo: RedisRepository = script_manager.redis_repo
        self._script_manager: RedisScriptManager | None = script_manager

    async def _acquire_token(self, key: str, config: TokenBucketConfig) -> bool:
        """带重试机制的令牌获取（修正版）"""
        try:
            start_time = time.time()

            while True:
                now = time.time()
                elapsed = now - start_time

                # 检查是否超时
                if elapsed >= config.timeout:
                    app_logger.warning(
                        f"Token acquisition timeout after {elapsed:.2f}s"
                    )
                    return False

                # 使用Lua脚本原子化操作
                result = await self._script_manager.execute(
                    script_name=self._script_key,
                    keys=[key],
                    args=[
                        now,
                        config.capacity,
                        config.fill_rate,
                        1,
                        config.timeout - elapsed,
                    ],
                )

                if result == 1:  # 成功获取
                    return True
                elif result == 2:  # 需要等待
                    # 计算精确等待时间
                    bucket_data = await self._repo.hmget(key, ["tokens", "last_time"])
                    last_time = float(bucket_data.get("last_time") or now)
                    current_tokens = min(
                        config.capacity,
                        (
                            float(bucket_data[0] or config.capacity)
                            + (now - last_time) * config.fill_rate
                        ),
                    )
                    wait_time = max(0, (1 - current_tokens) / config.fill_rate)

                    # 动态调整等待时间（不超过剩余超时时间）
                    actual_wait = min(
                        wait_time, config.timeout - elapsed - 0.1
                    )  # 预留0.1s缓冲
                    if actual_wait > 0:
                        app_logger.debug(
                            f"Waiting {actual_wait:.2f}s for token (remaining timeout: {config.timeout - elapsed:.2f}s)"
                        )
                    await asyncio.sleep(actual_wait)
                else:  # 彻底失败
                    return False

        except Exception as e:
            app_logger.error(f"Token bucket operation failed: {e}", exception=True)
            return True  # Redis故障时默认放行

    def __call__(self, config: Union[TokenBucketConfig, dict]):
        config = (
            config
            if isinstance(config, TokenBucketConfig)
            else TokenBucketConfig(**config)
        )

        def decorator(func: Callable[..., T]) -> Callable[..., T]:
            @wraps(func)
            async def wrapper(*args, **kwargs) -> T:
                # 生成限流键（可根据需要添加爬虫ID等参数）
                func_name = fullname(func)
                key = f"{config.key_prefix}:{func_name}"

                if not await self._acquire_token(key, config):
                    app_logger.info(f"Rate limit exceeded for {key}")

                return await func(*args, **kwargs)

            return wrapper

        return decorator
