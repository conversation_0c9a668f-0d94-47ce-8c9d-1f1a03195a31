import redis
import json
from datetime import datetime
from typing import Optional, Dict, Any, List


class RedisSessionManager:
    def __init__(self, redis_client: redis.Redis, default_ttl: int = 7200):
        self.redis = redis_client
        self.default_ttl = default_ttl

    def create_session(self, session_id: str, session_data: Dict[str, Any], ttl: Optional[int] = None) -> bool:
        """创建新会话"""
        key = f"session:{session_id}"
        session_data['created_at'] = datetime.now().isoformat()
        session_data['last_activity'] = datetime.now().isoformat()

        return self.redis.setex(
            key,
            ttl or self.default_ttl,
            json.dumps(session_data)
        )

    def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """获取会话数据"""
        key = f"session:{session_id}"
        data = self.redis.get(key)
        if data:
            session_data = json.loads(data)
            # 更新最后活动时间
            session_data['last_activity'] = datetime.now().isoformat()
            self.redis.setex(key, self.default_ttl, json.dumps(session_data))
            return session_data
        return None

    def delete_session(self, session_id: str) -> bool:
        """删除会话"""
        key = f"session:{session_id}"
        return bool(self.redis.delete(key))

    def get_user_sessions(self, user_id: str) -> List[Dict[str, Any]]:
        """获取用户的所有活跃会话"""
        pattern = "session:*"
        sessions = []
        for key in self.redis.scan_iter(match=pattern):
            data = self.redis.get(key)
            if data:
                session_data = json.loads(data)
                if session_data.get('user_id') == user_id:
                    session_data['session_id'] = key.decode().split(':')[1]
                    sessions.append(session_data)
        return sessions

    def invalidate_user_sessions(self, user_id: str, except_session_id: Optional[str] = None) -> int:
        """强制注销用户的所有会话（除了指定的会话）"""
        invalidated = 0
        for session in self.get_user_sessions(user_id):
            session_id = session['session_id']
            if except_session_id and session_id == except_session_id:
                continue
            if self.delete_session(session_id):
                invalidated += 1
        return invalidated
