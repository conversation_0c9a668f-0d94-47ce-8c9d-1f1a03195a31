import os
import time
from datetime import datetime

import psutil
from commonlib.schemas.responses import SuccessResponse, success_response
from fastapi import APIRouter
from pydantic import BaseModel, Field

startup_time = time.time()


class HealthCheckResponse(BaseModel):
    """系统健康检查响应"""

    status: str = Field(
        ..., examples=["ok", "degraded", "unhealthy"], description="当前系统状态"
    )
    uptime: int = Field(..., examples=[12345], description="系统启动后运行时间（秒）")


class VersionResponse(BaseModel):
    """服务版本信息响应"""

    version: str = Field(
        ..., examples=["1.0.3", "2.1.0-beta"], description="服务版本号（语义化版本）"
    )
    build: str = Field(
        ..., examples=["2025-05-26T10:00:00Z"], description="构建时间（ISO 8601格式）"
    )
    commit: str = Field(
        ...,
        examples=["abc1234", "feat:add-new-api"],
        description="最近一次提交哈希或标签",
    )


class MetricsResponse(BaseModel):
    """系统运行时指标响应"""

    cpu_percent: float = Field(
        ..., examples=[12.5, 90.2], description="CPU 使用率（百分比）", le=100
    )
    memory_percent: float = Field(
        ..., examples=[55.2], description="内存使用率（百分比）", le=100
    )
    process_count: int = Field(..., examples=[135], description="总进程数量")
    uptime: int = Field(..., examples=[12345], description="服务已运行秒数")


class HealthCheckResponseModel(SuccessResponse[HealthCheckResponse]):
    """健康检查成功响应模型"""

    data: HealthCheckResponse

    model_config = {
        "json_schema_extra": {
            "examples": [
                {
                    "status": "success",
                    "code": 200,
                    "message": "Request processed successfully",
                    "timestamp": "2025-05-26T12:00:00Z",
                    "data": {"status": "ok", "uptime": 86400},
                }
            ],
            "description": "系统健康检查接口返回的标准响应格式。",
        }
    }


class VersionResponseModel(SuccessResponse[VersionResponse]):
    """版本信息成功响应模型"""

    data: VersionResponse

    model_config = {
        "json_schema_extra": {
            "examples": [
                {
                    "status": "success",
                    "code": 200,
                    "message": "Request processed successfully",
                    "timestamp": "2025-05-26T12:00:00Z",
                    "data": {
                        "version": "2.0.1",
                        "build": "2025-05-26T10:00:00Z",
                        "commit": "a1b2c3d",
                    },
                }
            ],
            "description": "返回当前服务版本号、构建时间和最后提交记录等信息。",
        }
    }


class MetricsResponseModel(SuccessResponse[MetricsResponse]):
    """系统指标成功响应模型"""

    data: MetricsResponse

    model_config = {
        "json_schema_extra": {
            "examples": [
                {
                    "status": "success",
                    "code": 200,
                    "message": "Request processed successfully",
                    "timestamp": "2025-05-26T12:00:00Z",
                    "data": {
                        "process_id": 1234,
                        "cpu_percent": 32.5,
                        "memory_percent": 66.1,
                        "process_count": 145,
                        "uptime": 172800,
                    },
                }
            ],
            "description": "提供系统运行时资源使用状况和进程指标信息。",
        }
    }


router = APIRouter(
    prefix="/sys",
    tags=["System Status"],
)


@router.get(
    "/health",
    summary="健康检查",
    description="用于探测服务是否存活、就绪和响应正常。",
    response_model=HealthCheckResponseModel,
)
def health_check():
    return success_response(
        HealthCheckResponse(
            **{"status": "ok", "uptime": int(time.time() - startup_time)}
        ),
        message="监控检测探测成功!",
    )


@router.get(
    "/version",
    summary="版本信息",
    description="提供当前部署版本、构建时间、Git 提交哈希等信息。",
    response_model=VersionResponseModel,
)
def version_info():
    return success_response(
        VersionResponse(
            version=os.getenv("APP_VERSION", "1.0.0"),
            build=os.getenv("BUILD_TIME", datetime.now().isoformat()),
            commit=os.getenv("GIT_COMMIT", "dev-local"),
        )
    )


@router.get(
    "/metrics",
    summary="系统运行指标",
    description="获取当前 CPU、内存、进程数量、服务存活时长等系统关键指标。",
    response_model=MetricsResponseModel,
)
def metrics_info():
    psutil.Process(os.getpid())
    return success_response(
        MetricsResponse(
            cpu_percent=psutil.cpu_percent(interval=0.1),
            memory_percent=psutil.virtual_memory().percent,
            process_count=len(psutil.pids()),
            uptime=int(time.time() - startup_time),
        )
    )
