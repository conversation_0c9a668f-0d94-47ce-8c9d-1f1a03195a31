"""
领域模型混入类模块。

此模块提供可重用的混入类，为领域模型添加通用功能，
如软删除、租户感知、审计跟踪和状态管理等。
"""

from datetime import datetime, timezone
from typing import Optional

from sqlalchemy import DateTime, func
from sqlalchemy.orm import Mapped, mapped_column

from .constants import CommonStatus
from .fields import Fields


# ================================
# 核心混入类
# ================================
class TimestampMixin:
    """提供 created_at 和 updated_at 时间戳字段的 mixin 类"""

    created_at: Mapped[datetime] = Fields.created_at()
    updated_at: Mapped[datetime] = Fields.updated_at()


class SoftDeleteMixin:
    """软删除功能混入类。
    
    添加deleted_at时间戳字段，并提供软删除和恢复记录的方法。
    
    属性:
        deleted_at: 记录被软删除的时间戳
    """

    deleted_at: Mapped[Optional[datetime]] = Fields.deleted_at()

    @property
    def is_deleted(self) -> bool:
        """检查记录是否已被软删除。
        
        Returns:
            如果记录已删除返回True，否则返回False
        """
        return self.deleted_at is not None

    @property
    def is_active(self) -> bool:
        """检查记录是否处于活跃状态（未删除）。
        
        Returns:
            如果记录活跃返回True，否则返回False
        """
        return self.deleted_at is None

    def soft_delete(self) -> None:
        """执行软删除操作。
        
        如果记录尚未删除，则将deleted_at时间戳设置为当前UTC时间。
        """
        if not self.is_deleted:
            self.deleted_at = datetime.now(timezone.utc)

    def restore(self) -> None:
        """恢复已软删除的记录。
        
        清除deleted_at时间戳以恢复记录。
        """
        if self.is_deleted:
            self.deleted_at = None

    @classmethod
    def active_filter(cls):
        """返回未软删除的过滤条件，用于SQLAlchemy查询"""
        return cls.deleted_at.is_(None)


class TenantMixin:
    """多租户支持混入类。
    
    添加tenant_id字段用于多租户数据隔离。
    
    属性:
        tenant_id: 用于数据隔离的租户标识符
    """
    tenant_id: Mapped[str] = Fields.tenant()


class AuditMixin:
    """审计跟踪功能混入类。
    
    添加created_by和updated_by字段来跟踪记录的创建者和最后修改者。
    
    属性:
        created_by: 创建记录的用户ID
        updated_by: 最后更新记录的用户ID
    """
    created_by: Mapped[Optional[str]] = Fields.created_by()
    updated_by: Mapped[Optional[str]] = Fields.updated_by()


class StatusMixin:
    """状态管理混入类。
    
    添加status字段用于通用状态值管理。
    
    属性:
        status: 记录的当前状态
    """
    status: Mapped[str] = Fields.status()


# ================================
# 组合混入类
# ================================

class FullAuditMixin(TenantMixin, AuditMixin, StatusMixin):
    """完整审计混入类，组合租户、审计和状态功能。
    
    此混入类提供全面的跟踪能力，包括：
    - 多租户支持 (tenant_id)
    - 审计跟踪 (created_by, updated_by)
    - 状态管理 (status)
    """
    pass


# ================================
# 管理器类
# ================================

class StatusManager:
    """具有状态字段的模型的状态管理工具。
    
    为状态管理和验证提供便捷方法。
    """

    def __init__(self, model_instance):
        """初始化状态管理器。

        Args:
            model_instance: 必须具有'status'属性的模型实例

        Raises:
            ValueError: 如果模型实例没有status属性
        """
        self.model = model_instance
        if not hasattr(model_instance, "status"):
            raise ValueError("模型实例必须具有'status'属性")

    def is_status(self, status: str) -> bool:
        """检查模型是否具有指定状态。
        
        Args:
            status: 要检查的状态值
            
        Returns:
            如果模型具有指定状态返回True
        """
        return self.model.status == status

    def set_status(self, status: str, user_id: Optional[str] = None) -> None:
        """设置状态并可选地更新审计字段。
        
        Args:
            status: 新的状态值
            user_id: 用于审计跟踪的用户ID（如果模型支持）
        """
        self.model.status = status
        if user_id and hasattr(self.model, "updated_by"):
            self.model.updated_by = user_id

    def activate(self, user_id: Optional[str] = None) -> None:
        """将状态设置为激活。
        
        Args:
            user_id: 用于审计跟踪的用户ID
        """
        self.set_status(CommonStatus.ACTIVE, user_id)

    def deactivate(self, user_id: Optional[str] = None) -> None:
        """将状态设置为非激活。
        
        Args:
            user_id: 用于审计跟踪的用户ID
        """
        self.set_status(CommonStatus.INACTIVE, user_id)

    def suspend(self, user_id: Optional[str] = None) -> None:
        """将状态设置为暂停。
        
        Args:
            user_id: 用于审计跟踪的用户ID
        """
        self.set_status(CommonStatus.SUSPENDED, user_id)

    @property
    def is_active(self) -> bool:
        """检查模型是否处于激活状态。
        
        Returns:
            如果状态为激活返回True
        """
        return self.model.status == CommonStatus.ACTIVE

    @property
    def is_available(self) -> bool:
        """检查模型是否可用（激活且未删除）。
        
        Returns:
            如果模型激活且未软删除返回True
        """
        return (
                self.model.status == CommonStatus.ACTIVE
                and not getattr(self.model, "is_deleted", False)
        )


class AuditManager:
    """具有审计字段的模型的审计管理工具。
    
    提供设置审计信息的便捷方法。
    """

    def __init__(self, model_instance):
        """初始化审计管理器。

        Args:
            model_instance: 具有审计字段的模型实例
        """
        self.model = model_instance

    def set_audit_fields(
            self,
            user_id: Optional[str] = None,
            is_update: bool = False
    ) -> None:
        """根据操作类型设置审计字段。

        Args:
            user_id: 执行操作的用户ID
            is_update: 是否为更新操作（相对于创建操作）
        """
        if user_id:
            if not is_update and hasattr(self.model, "created_by"):
                self.model.created_by = user_id
            if hasattr(self.model, "updated_by"):
                self.model.updated_by = user_id

    def set_creator(self, user_id: str) -> None:
        """设置记录的创建者。
        
        Args:
            user_id: 创建者的用户ID
        """
        if hasattr(self.model, "created_by"):
            self.model.created_by = user_id

    def set_updater(self, user_id: str) -> None:
        """设置记录的最后更新者。
        
        Args:
            user_id: 更新者的用户ID
        """
        if hasattr(self.model, "updated_by"):
            self.model.updated_by = user_id
