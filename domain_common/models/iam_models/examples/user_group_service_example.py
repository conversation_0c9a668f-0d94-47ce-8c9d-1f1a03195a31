"""
用户组服务示例

演示如何使用新的用户组功能进行权限管理和组织结构映射
"""

import uuid
from datetime import datetime, date
from typing import Dict, Any, Optional, List
from sqlalchemy import select, func, and_, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import IntegrityError

from domain_common.models import CommonStatus
from domain_common.models.constants import UserGroupType, AssignmentType, RoleType
from domain_common.models.iam_models import (
    UserGroup, User, Role, Permission,
    UserGroupMember, UserGroupRole, UserGroupPermission,
    UserRole, RolePermission
)


class UserGroupService:
    """用户组服务
    
    提供用户组管理、成员管理、权限分配等功能
    """
    
    def __init__(self, session: AsyncSession):
        self.session = session
    
    # ================================
    # 用户组管理
    # ================================
    
    async def create_user_group(
        self,
        tenant_id: str,
        group_name: str,
        group_code: str,
        group_type: str,
        description: Optional[str] = None,
        parent_group_id: Optional[str] = None,
        max_members: int = 0,
        created_by: Optional[str] = None
    ) -> UserGroup:
        """创建用户组"""
        
        # 验证用户组类型
        if group_type not in UserGroupType.ALL_TYPES:
            raise ValueError(f"无效的用户组类型: {group_type}")
        
        # 验证父用户组
        if parent_group_id:
            parent_group = await self.get_user_group_by_id(parent_group_id)
            if not parent_group or parent_group.tenant_id != tenant_id:
                raise ValueError("父用户组不存在或不属于同一租户")
        
        # 创建用户组
        user_group = UserGroup(
            group_id=str(uuid.uuid4()),
            tenant_id=tenant_id,
            group_name=group_name,
            group_code=group_code,
            description=description,
            group_type=group_type,
            parent_group_id=parent_group_id,
            max_members=max_members,
            status=CommonStatus.ACTIVE,
            created_by=created_by
        )
        
        try:
            self.session.add(user_group)
            await self.session.commit()
            return user_group
        except IntegrityError:
            await self.session.rollback()
            raise ValueError("用户组编码已存在")
    
    async def get_user_group_by_id(self, group_id: str) -> Optional[UserGroup]:
        """根据ID获取用户组"""
        return await UserGroup.get_group_by_id(self.session, group_id)
    
    async def get_user_groups_by_tenant(
        self, 
        tenant_id: str,
        group_type: Optional[str] = None,
        parent_group_id: Optional[str] = None
    ) -> List[UserGroup]:
        """获取租户下的用户组列表"""
        
        conditions = [
            UserGroup.tenant_id == tenant_id,
            UserGroup.status == CommonStatus.ACTIVE
        ]
        
        if group_type:
            conditions.append(UserGroup.group_type == group_type)
        
        if parent_group_id:
            conditions.append(UserGroup.parent_group_id == parent_group_id)
        
        stmt = select(UserGroup).where(and_(*conditions))
        result = await self.session.execute(stmt)
        return result.scalars().all()
    
    # ================================
    # 用户组成员管理
    # ================================
    
    async def add_user_to_group(
        self,
        tenant_id: str,
        user_id: str,
        group_id: str,
        assignment_type: str = AssignmentType.PERMANENT,
        effective_date: Optional[date] = None,
        expiry_date: Optional[date] = None,
        assigned_by: Optional[str] = None
    ) -> UserGroupMember:
        """将用户添加到用户组"""
        
        # 验证用户组和用户
        user_group = await self.get_user_group_by_id(group_id)
        if not user_group or user_group.tenant_id != tenant_id:
            raise ValueError("用户组不存在或不属于同一租户")
        
        # 检查最大成员数限制
        if user_group.max_members > 0:
            current_count = await self._get_group_member_count(group_id)
            if current_count >= user_group.max_members:
                raise ValueError("用户组成员数已达到上限")
        
        # 创建成员关联
        member = UserGroupMember(
            tenant_id=tenant_id,
            user_id=user_id,
            group_id=group_id,
            assignment_type=assignment_type,
            effective_date=effective_date,
            expiry_date=expiry_date,
            status=CommonStatus.ACTIVE,
            assigned_by=assigned_by
        )
        
        try:
            self.session.add(member)
            await self.session.commit()
            return member
        except IntegrityError:
            await self.session.rollback()
            raise ValueError("用户已在该用户组中")
    
    async def remove_user_from_group(
        self,
        tenant_id: str,
        user_id: str,
        group_id: str
    ) -> bool:
        """从用户组中移除用户"""
        
        stmt = select(UserGroupMember).where(
            and_(
                UserGroupMember.tenant_id == tenant_id,
                UserGroupMember.user_id == user_id,
                UserGroupMember.group_id == group_id,
                UserGroupMember.status == CommonStatus.ACTIVE
            )
        )
        result = await self.session.execute(stmt)
        member = result.scalar_one_or_none()
        
        if member:
            member.status = CommonStatus.DELETED
            member.deleted_at = datetime.utcnow()
            await self.session.commit()
            return True
        
        return False
    
    async def get_user_groups(self, tenant_id: str, user_id: str) -> List[Dict[str, Any]]:
        """获取用户所属的用户组列表"""
        
        stmt = (
            select(
                UserGroup.group_id,
                UserGroup.group_name,
                UserGroup.group_code,
                UserGroup.group_type,
                UserGroupMember.assignment_type,
                UserGroupMember.effective_date,
                UserGroupMember.expiry_date
            )
            .join(UserGroupMember)
            .where(
                and_(
                    UserGroupMember.tenant_id == tenant_id,
                    UserGroupMember.user_id == user_id,
                    UserGroupMember.status == CommonStatus.ACTIVE,
                    UserGroup.status == CommonStatus.ACTIVE,
                    or_(
                        UserGroupMember.expiry_date.is_(None),
                        UserGroupMember.expiry_date > datetime.utcnow().date()
                    )
                )
            )
        )
        
        result = await self.session.execute(stmt)
        return [
            {
                "group_id": row.group_id,
                "group_name": row.group_name,
                "group_code": row.group_code,
                "group_type": row.group_type,
                "assignment_type": row.assignment_type,
                "effective_date": row.effective_date.isoformat() if row.effective_date else None,
                "expiry_date": row.expiry_date.isoformat() if row.expiry_date else None
            }
            for row in result
        ]
    
    # ================================
    # 用户组权限管理
    # ================================
    
    async def assign_role_to_group(
        self,
        tenant_id: str,
        group_id: str,
        role_id: str,
        assignment_type: str = AssignmentType.PERMANENT,
        assigned_by: Optional[str] = None
    ) -> UserGroupRole:
        """为用户组分配角色"""
        
        # 验证用户组和角色
        user_group = await self.get_user_group_by_id(group_id)
        if not user_group or user_group.tenant_id != tenant_id:
            raise ValueError("用户组不存在或不属于同一租户")
        
        # 创建角色关联
        group_role = UserGroupRole(
            tenant_id=tenant_id,
            group_id=group_id,
            role_id=role_id,
            assignment_type=assignment_type,
            status=CommonStatus.ACTIVE,
            assigned_by=assigned_by
        )
        
        try:
            self.session.add(group_role)
            await self.session.commit()
            return group_role
        except IntegrityError:
            await self.session.rollback()
            raise ValueError("角色已分配给该用户组")
    
    async def get_user_effective_permissions(
        self, 
        tenant_id: str, 
        user_id: str
    ) -> Dict[str, List[str]]:
        """获取用户的有效权限（包括通过用户组继承的权限）"""
        
        permissions = {
            "direct_permissions": [],      # 直接分配的权限
            "role_permissions": [],        # 通过角色获得的权限
            "group_permissions": [],       # 通过用户组获得的权限
            "group_role_permissions": []   # 通过用户组角色获得的权限
        }
        
        # 1. 获取直接分配的角色权限
        direct_role_stmt = (
            select(Permission.permission_code)
            .join(RolePermission)
            .join(UserRole)
            .where(
                and_(
                    UserRole.tenant_id == tenant_id,
                    UserRole.user_id == user_id,
                    UserRole.status == CommonStatus.ACTIVE,
                    RolePermission.status == CommonStatus.ACTIVE,
                    Permission.status == CommonStatus.ACTIVE
                )
            )
        )
        result = await self.session.execute(direct_role_stmt)
        permissions["role_permissions"] = [row[0] for row in result]
        
        # 2. 获取通过用户组角色继承的权限
        group_role_stmt = (
            select(Permission.permission_code)
            .join(RolePermission)
            .join(UserGroupRole)
            .join(UserGroupMember)
            .where(
                and_(
                    UserGroupMember.tenant_id == tenant_id,
                    UserGroupMember.user_id == user_id,
                    UserGroupMember.status == CommonStatus.ACTIVE,
                    UserGroupRole.status == CommonStatus.ACTIVE,
                    RolePermission.status == CommonStatus.ACTIVE,
                    Permission.status == CommonStatus.ACTIVE
                )
            )
        )
        result = await self.session.execute(group_role_stmt)
        permissions["group_role_permissions"] = [row[0] for row in result]
        
        # 3. 获取通过用户组直接分配的权限
        group_permission_stmt = (
            select(Permission.permission_code)
            .join(UserGroupPermission)
            .join(UserGroupMember)
            .where(
                and_(
                    UserGroupMember.tenant_id == tenant_id,
                    UserGroupMember.user_id == user_id,
                    UserGroupMember.status == CommonStatus.ACTIVE,
                    UserGroupPermission.status == CommonStatus.ACTIVE,
                    Permission.status == CommonStatus.ACTIVE
                )
            )
        )
        result = await self.session.execute(group_permission_stmt)
        permissions["group_permissions"] = [row[0] for row in result]
        
        return permissions
    
    # ================================
    # 辅助方法
    # ================================
    
    async def _get_group_member_count(self, group_id: str) -> int:
        """获取用户组成员数量"""
        stmt = select(func.count(UserGroupMember.id)).where(
            and_(
                UserGroupMember.group_id == group_id,
                UserGroupMember.status == CommonStatus.ACTIVE
            )
        )
        result = await self.session.execute(stmt)
        return result.scalar() or 0


# ================================
# 使用示例
# ================================

async def example_usage():
    """用户组服务使用示例"""
    
    # 假设已有session
    session = None  # AsyncSession实例
    service = UserGroupService(session)
    
    tenant_id = "tenant_123"
    
    # 1. 创建部门用户组
    dept_group = await service.create_user_group(
        tenant_id=tenant_id,
        group_name="技术部",
        group_code="TECH_DEPT",
        group_type=UserGroupType.DEPARTMENT,
        description="技术部门用户组",
        max_members=50
    )
    
    # 2. 创建项目组
    project_group = await service.create_user_group(
        tenant_id=tenant_id,
        group_name="AI项目组",
        group_code="AI_PROJECT",
        group_type=UserGroupType.PROJECT,
        description="AI项目开发组",
        parent_group_id=dept_group.group_id
    )
    
    # 3. 添加用户到用户组
    await service.add_user_to_group(
        tenant_id=tenant_id,
        user_id="user_123",
        group_id=project_group.group_id,
        assignment_type=AssignmentType.PERMANENT
    )
    
    # 4. 为用户组分配角色
    await service.assign_role_to_group(
        tenant_id=tenant_id,
        group_id=project_group.group_id,
        role_id="developer_role_id"
    )
    
    # 5. 获取用户的有效权限
    user_permissions = await service.get_user_effective_permissions(
        tenant_id=tenant_id,
        user_id="user_123"
    )
    
    print("用户有效权限:", user_permissions)
