-- 数据库迁移脚本：添加用户组和平台角色支持
-- 版本：v1.0
-- 日期：2025-07-29
-- 描述：为IAM系统添加用户组概念和平台/租户管理员角色区分

-- ================================
-- 1. 修改现有角色表，添加平台角色支持
-- ================================

-- 添加角色类型相关字段
ALTER TABLE roles 
ADD COLUMN role_type VARCHAR(50) NOT NULL DEFAULT 'custom',
ADD COLUMN is_platform_role BOOLEAN NOT NULL DEFAULT FALSE,
ADD COLUMN is_system_role BOOLEAN NOT NULL DEFAULT FALSE;

-- 修改tenant_id字段，允许平台角色为NULL
ALTER TABLE roles ALTER COLUMN tenant_id DROP NOT NULL;

-- 添加新的索引
CREATE INDEX idx_roles_type ON roles(role_type);
CREATE INDEX idx_roles_platform ON roles(is_platform_role);
CREATE INDEX idx_roles_system ON roles(is_system_role);

-- 添加平台级角色的唯一约束
CREATE UNIQUE INDEX uq_roles_platform_code ON roles(role_code) 
WHERE tenant_id IS NULL;

-- 更新现有角色的role_type字段
UPDATE roles SET role_type = 'custom' WHERE role_type IS NULL;

-- ================================
-- 2. 创建用户组表
-- ================================

CREATE TABLE user_groups (
    group_id VARCHAR(64) PRIMARY KEY DEFAULT uuid_generate_v4()::varchar,
    tenant_id VARCHAR(64) NOT NULL,
    group_name VARCHAR(255) NOT NULL,
    group_code VARCHAR(100) NOT NULL,
    description TEXT,
    group_type VARCHAR(50) NOT NULL,
    level INTEGER NOT NULL DEFAULT 1,
    parent_group_id VARCHAR(64),
    max_members INTEGER NOT NULL DEFAULT 0,
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    meta_data JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT current_timestamp(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT current_timestamp(),
    created_by VARCHAR(64),
    updated_by VARCHAR(64),
    
    -- 约束
    CONSTRAINT uq_user_groups_tenant_code UNIQUE (tenant_id, group_code),
    CONSTRAINT fk_user_groups_tenant FOREIGN KEY (tenant_id) REFERENCES tenants(tenant_id),
    CONSTRAINT fk_user_groups_parent FOREIGN KEY (parent_group_id) REFERENCES user_groups(group_id)
);

-- 用户组表索引
CREATE INDEX idx_user_groups_tenant_type ON user_groups(tenant_id, group_type);
CREATE INDEX idx_user_groups_parent ON user_groups(parent_group_id);
CREATE INDEX idx_user_groups_tenant_status ON user_groups(tenant_id, status);
CREATE INDEX idx_user_groups_level ON user_groups(level);

-- ================================
-- 3. 创建用户组成员关联表
-- ================================

CREATE TABLE user_group_members (
    id BIGSERIAL PRIMARY KEY,
    tenant_id VARCHAR(64) NOT NULL,
    user_id VARCHAR(64) NOT NULL,
    group_id VARCHAR(64) NOT NULL,
    assignment_type VARCHAR(20) NOT NULL DEFAULT 'permanent',
    effective_date DATE,
    expiry_date DATE,
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    assigned_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT current_timestamp(),
    assigned_by VARCHAR(64),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT current_timestamp(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT current_timestamp(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    -- 约束
    CONSTRAINT uq_user_group_members_tenant_user_group UNIQUE (tenant_id, user_id, group_id),
    CONSTRAINT fk_user_group_members_tenant FOREIGN KEY (tenant_id) REFERENCES tenants(tenant_id),
    CONSTRAINT fk_user_group_members_user FOREIGN KEY (user_id) REFERENCES users(user_id),
    CONSTRAINT fk_user_group_members_group FOREIGN KEY (group_id) REFERENCES user_groups(group_id)
);

-- 用户组成员表索引
CREATE INDEX idx_user_group_members_tenant_user ON user_group_members(tenant_id, user_id);
CREATE INDEX idx_user_group_members_tenant_group ON user_group_members(tenant_id, group_id);
CREATE INDEX idx_user_group_members_status ON user_group_members(status);
CREATE INDEX idx_user_group_members_expiry ON user_group_members(expiry_date);

-- ================================
-- 4. 创建用户组角色关联表
-- ================================

CREATE TABLE user_group_roles (
    id BIGSERIAL PRIMARY KEY,
    tenant_id VARCHAR(64) NOT NULL,
    group_id VARCHAR(64) NOT NULL,
    role_id VARCHAR(64) NOT NULL,
    assignment_type VARCHAR(20) NOT NULL DEFAULT 'permanent',
    effective_date DATE,
    expiry_date DATE,
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    assigned_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT current_timestamp(),
    assigned_by VARCHAR(64),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT current_timestamp(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT current_timestamp(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    -- 约束
    CONSTRAINT uq_user_group_roles_tenant_group_role UNIQUE (tenant_id, group_id, role_id),
    CONSTRAINT fk_user_group_roles_tenant FOREIGN KEY (tenant_id) REFERENCES tenants(tenant_id),
    CONSTRAINT fk_user_group_roles_group FOREIGN KEY (group_id) REFERENCES user_groups(group_id),
    CONSTRAINT fk_user_group_roles_role FOREIGN KEY (role_id) REFERENCES roles(role_id)
);

-- 用户组角色表索引
CREATE INDEX idx_user_group_roles_tenant_group ON user_group_roles(tenant_id, group_id);
CREATE INDEX idx_user_group_roles_tenant_role ON user_group_roles(tenant_id, role_id);
CREATE INDEX idx_user_group_roles_status ON user_group_roles(status);
CREATE INDEX idx_user_group_roles_expiry ON user_group_roles(expiry_date);

-- ================================
-- 5. 创建用户组权限关联表
-- ================================

CREATE TABLE user_group_permissions (
    id BIGSERIAL PRIMARY KEY,
    tenant_id VARCHAR(64) NOT NULL,
    group_id VARCHAR(64) NOT NULL,
    permission_id VARCHAR(64) NOT NULL,
    assignment_type VARCHAR(20) NOT NULL DEFAULT 'permanent',
    effective_date DATE,
    expiry_date DATE,
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    assigned_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT current_timestamp(),
    assigned_by VARCHAR(64),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT current_timestamp(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT current_timestamp(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    -- 约束
    CONSTRAINT uq_user_group_permissions_tenant_group_permission UNIQUE (tenant_id, group_id, permission_id),
    CONSTRAINT fk_user_group_permissions_tenant FOREIGN KEY (tenant_id) REFERENCES tenants(tenant_id),
    CONSTRAINT fk_user_group_permissions_group FOREIGN KEY (group_id) REFERENCES user_groups(group_id),
    CONSTRAINT fk_user_group_permissions_permission FOREIGN KEY (permission_id) REFERENCES permissions(permission_id)
);

-- 用户组权限表索引
CREATE INDEX idx_user_group_permissions_tenant_group ON user_group_permissions(tenant_id, group_id);
CREATE INDEX idx_user_group_permissions_tenant_permission ON user_group_permissions(tenant_id, permission_id);
CREATE INDEX idx_user_group_permissions_status ON user_group_permissions(status);
CREATE INDEX idx_user_group_permissions_expiry ON user_group_permissions(expiry_date);

-- ================================
-- 6. 插入系统预定义角色
-- ================================

-- 插入平台超级管理员角色
INSERT INTO roles (
    role_id, tenant_id, role_name, role_code, description,
    role_type, is_platform_role, is_system_role, level, status
) VALUES (
    uuid_generate_v4()::varchar, NULL, '平台超级管理员', 'PLATFORM_SUPER_ADMIN',
    '拥有对所有租户的元权限，可以管理整个平台',
    'platform_super_admin', TRUE, TRUE, 1, 'active'
);

-- 插入平台管理员角色
INSERT INTO roles (
    role_id, tenant_id, role_name, role_code, description,
    role_type, is_platform_role, is_system_role, level, status
) VALUES (
    uuid_generate_v4()::varchar, NULL, '平台管理员', 'PLATFORM_ADMIN',
    '平台级管理员，具有平台管理权限但不能跨租户操作',
    'platform_admin', TRUE, TRUE, 2, 'active'
);

-- ================================
-- 7. 为每个现有租户创建租户超级管理员角色
-- ================================

INSERT INTO roles (
    role_id, tenant_id, role_name, role_code, description,
    role_type, is_platform_role, is_system_role, level, status
)
SELECT 
    uuid_generate_v4()::varchar,
    tenant_id,
    '租户超级管理员',
    'TENANT_SUPER_ADMIN',
    '租户内的超级管理员，拥有该租户下的所有操作权限',
    'tenant_super_admin',
    FALSE,
    TRUE,
    1,
    'active'
FROM tenants 
WHERE status = 'active';

-- ================================
-- 8. 添加注释
-- ================================

COMMENT ON TABLE user_groups IS '用户组表，支持组织结构映射和权限复用';
COMMENT ON TABLE user_group_members IS '用户组成员关联表，管理用户与用户组的关系';
COMMENT ON TABLE user_group_roles IS '用户组角色关联表，实现权限复用';
COMMENT ON TABLE user_group_permissions IS '用户组权限关联表，支持直接权限分配';

COMMENT ON COLUMN roles.role_type IS '角色类型：platform_super_admin, tenant_super_admin等';
COMMENT ON COLUMN roles.is_platform_role IS '是否为平台级角色';
COMMENT ON COLUMN roles.is_system_role IS '是否为系统内置角色（不可删除）';
