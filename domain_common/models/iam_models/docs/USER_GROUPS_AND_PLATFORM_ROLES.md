# 用户组和平台角色功能说明

## 概述

本文档描述了IAM系统中新增的用户组概念和平台/租户管理员角色区分功能。这些功能旨在实现更灵活的权限管理和组织结构映射。

## 🎯 功能目标

### 1. 用户组概念
- **权限复用**：将一组权限绑定在用户组上，简化权限管理
- **组织映射**：例如部门、小组、项目组等自然映射为用户组
- **跨角色复合**：用户组可包含多个角色/权限，使得管理灵活
- **租户隔离**：每个租户拥有独立的用户组集合

### 2. 平台/租户管理员角色区分
- **平台超级管理员**：拥有对所有租户的元权限
- **租户超级管理员**：拥有该租户下的所有操作权限
- **角色层级**：明确区分平台级和租户级角色

## 📊 数据模型设计

### 核心实体

#### 1. UserGroup（用户组）
```python
class UserGroup:
    group_id: str           # 用户组ID
    tenant_id: str          # 租户ID
    group_name: str         # 用户组名称
    group_code: str         # 用户组编码
    group_type: str         # 用户组类型
    level: int              # 层级
    parent_group_id: str    # 父用户组ID
    max_members: int        # 最大成员数
    status: str             # 状态
    meta_data: dict         # 元数据
```

#### 2. Role（角色扩展）
```python
class Role:
    # 原有字段...
    role_type: str          # 角色类型
    is_platform_role: bool  # 是否为平台级角色
    is_system_role: bool    # 是否为系统内置角色
    tenant_id: str          # 租户ID（平台角色可为NULL）
```

### 关联关系

#### 1. UserGroupMember（用户组成员）
- 管理用户与用户组的多对多关系
- 支持临时成员分配和过期时间

#### 2. UserGroupRole（用户组角色）
- 管理用户组与角色的多对多关系
- 实现权限复用

#### 3. UserGroupPermission（用户组权限）
- 管理用户组与权限的多对多关系
- 支持直接权限分配

## 🔧 角色类型定义

### 平台级角色
- `PLATFORM_SUPER_ADMIN`：平台超级管理员
- `PLATFORM_ADMIN`：平台管理员

### 租户级角色
- `TENANT_SUPER_ADMIN`：租户超级管理员
- `TENANT_ADMIN`：租户管理员
- `TENANT_USER`：租户普通用户
- `CUSTOM`：自定义角色

### 用户组类型
- `DEPARTMENT`：部门
- `TEAM`：小组/团队
- `PROJECT`：项目组
- `ROLE_GROUP`：角色组（权限复用）
- `PERMISSION_GROUP`：权限组
- `TEMPORARY`：临时组

## 🚀 使用场景

### 场景1：部门权限管理
```python
# 1. 创建技术部用户组
tech_dept = await service.create_user_group(
    tenant_id="tenant_123",
    group_name="技术部",
    group_code="TECH_DEPT",
    group_type=UserGroupType.DEPARTMENT
)

# 2. 为技术部分配开发者角色
await service.assign_role_to_group(
    tenant_id="tenant_123",
    group_id=tech_dept.group_id,
    role_id="developer_role_id"
)

# 3. 将用户加入技术部
await service.add_user_to_group(
    tenant_id="tenant_123",
    user_id="user_123",
    group_id=tech_dept.group_id
)
```

### 场景2：项目组权限管理
```python
# 1. 创建AI项目组（隶属于技术部）
ai_project = await service.create_user_group(
    tenant_id="tenant_123",
    group_name="AI项目组",
    group_code="AI_PROJECT",
    group_type=UserGroupType.PROJECT,
    parent_group_id=tech_dept.group_id
)

# 2. 为项目组分配特定权限
await service.assign_permission_to_group(
    tenant_id="tenant_123",
    group_id=ai_project.group_id,
    permission_id="ai_model_access"
)
```

### 场景3：平台管理员设置
```python
# 1. 创建平台超级管理员角色（系统预定义）
platform_admin_role = Role(
    role_id="platform_super_admin_id",
    tenant_id=None,  # 平台级角色
    role_name="平台超级管理员",
    role_code="PLATFORM_SUPER_ADMIN",
    role_type=RoleType.PLATFORM_SUPER_ADMIN,
    is_platform_role=True,
    is_system_role=True
)

# 2. 为用户分配平台管理员角色
await rbac_service.assign_role_to_user(
    tenant_id=None,  # 平台级分配
    user_id="platform_admin_user",
    role_id="platform_super_admin_id"
)
```

## 🔐 权限继承机制

### 用户权限来源
1. **直接角色权限**：用户直接分配的角色权限
2. **用户组角色权限**：通过用户组角色继承的权限
3. **用户组直接权限**：用户组直接分配的权限
4. **层级继承权限**：从父用户组继承的权限

### 权限计算优先级
1. 明确拒绝 > 明确允许 > 继承允许
2. 直接分配 > 用户组分配 > 层级继承
3. 临时权限 > 永久权限

## 📋 数据库迁移

### 必要的数据库变更
1. **修改roles表**：添加角色类型相关字段
2. **创建user_groups表**：用户组主表
3. **创建关联表**：用户组成员、角色、权限关联表
4. **插入系统角色**：预定义平台和租户管理员角色

### 迁移脚本
参考 `migrations/add_user_groups_and_platform_roles.sql`

## 🔍 API接口示例

### 用户组管理
```python
# 创建用户组
POST /api/v1/user-groups
{
    "group_name": "技术部",
    "group_code": "TECH_DEPT",
    "group_type": "department",
    "description": "技术部门用户组"
}

# 获取用户组列表
GET /api/v1/user-groups?tenant_id=xxx&group_type=department

# 添加用户到用户组
POST /api/v1/user-groups/{group_id}/members
{
    "user_id": "user_123",
    "assignment_type": "permanent"
}
```

### 权限管理
```python
# 为用户组分配角色
POST /api/v1/user-groups/{group_id}/roles
{
    "role_id": "developer_role_id",
    "assignment_type": "permanent"
}

# 获取用户有效权限
GET /api/v1/users/{user_id}/effective-permissions
```

## ⚠️ 注意事项

### 1. 数据一致性
- 用户组删除时需要处理成员和权限关联
- 角色类型变更需要验证权限兼容性
- 平台角色的tenant_id必须为NULL

### 2. 性能考虑
- 权限计算可能涉及多表关联，建议使用缓存
- 用户组层级不宜过深，建议限制在3-4层
- 大型用户组的成员变更需要考虑批量操作

### 3. 安全考虑
- 平台超级管理员权限需要严格控制
- 用户组权限变更需要审计日志
- 临时权限需要定期清理过期数据

## 🔄 后续扩展

### 1. 动态权限策略
- 基于属性的访问控制（ABAC）
- 条件权限和上下文感知

### 2. 权限审计
- 权限变更历史追踪
- 权限使用情况分析

### 3. 组织结构同步
- 与外部系统（如AD、LDAP）同步
- 自动化用户组管理

## 📚 相关文档

- [RBAC服务完整指南](./README_RBAC_COMPLETE.md)
- [IAM模型使用指南](./README.md)
- [数据库迁移指南](../migrations/README.md)
- [API接口文档](../../routes/README.md)
