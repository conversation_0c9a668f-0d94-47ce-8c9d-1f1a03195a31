# IAM 服务模型使用指南

## 模型概览

IAM 服务模型按业务功能划分为以下几个模块：

1. **核心业务模型** (`core.py`)
   - `Tenant`: 租户实体
   - `User`: 用户实体
   - `Role`: 角色实体
   - `Permission`: 权限实体

2. **关联关系模型** (`relations.py`)
   - `UserRole`: 用户角色关联
   - `RolePermission`: 角色权限关联
   - `UserPolicy`: 用户策略关联

3. **认证安全模型** (`auth.py`)
   - `UserSession`: 用户会话
   - `UserMFA`: 多因子认证
   - `VerificationCode`: 验证码
   - `PasswordHistory`: 密码历史

4. **策略模型** (`policies.py`)
   - `PermissionPolicy`: 权限策略

5. **任务模型** (`tasks.py`)
   - `BatchTask`: 批量任务

6. **审计模型** (`audit.py`)
   - `AuditLog`: 审计日志

7. **系统模型** (`system.py`)
   - `SystemConfig`: 系统配置
   - `CacheKey`: 缓存管理

## 基类说明

所有模型都基于 `domain_common/models/base_model.py` 中定义的基类：

- `Base`: 基础实体类，提供 ID 和时间戳字段
- `MultiTenantBase`: 多租户基类，添加 `tenant_id` 字段
- `AuditableBase`: 可审计基类，添加 `created_by`, `updated_by` 字段
- `IAMEntityBase`: IAM 专用基类，添加 `status` 字段和状态管理方法

## 使用示例

### 1. 导入模型

```python
# 导入单个模型
from domain_common.models.iam_models import User, Role, Permission

# 或者按模块导入
from domain_common.models.iam_models.core import User, Role
from domain_common.models.iam_models.auth import UserSession
```

### 2. 创建租户

```python
from domain_common.models.iam_models import Tenant

# 创建租户
tenant = Tenant(
    tenant_name="示例公司",
    tenant_code="example_corp",
    description="这是一个示例租户",
    max_users=500
)

# 设置 JSONB 字段
tenant.set_jsonb_value('settings', 'theme', 'dark')
tenant.set_jsonb_value('settings', 'language', 'zh-CN')

# 添加到会话并提交
session.add(tenant)
session.commit()
```

### 3. 创建用户

```python
from domain_common.models.iam_models import User

# 创建用户
user = User(
    tenant_id=tenant.tenant_id,
    username="john_doe",
    email="<EMAIL>",
    password_hash="hashed_password",  # 实际应用中应使用安全的哈希函数
    salt="random_salt"  # 实际应用中应随机生成
)

# 设置用户资料
user.set_jsonb_value('profile', 'full_name', 'John Doe')
user.set_jsonb_value('profile', 'department', 'IT')

# 设置审计字段
user.set_audit_fields('admin_user_id')

# 添加到会话并提交
session.add(user)
session.commit()
```

### 4. 创建角色和权限

```python
from domain_common.models.iam_models import Role, Permission

# 创建角色
role = Role(
    tenant_id=tenant.tenant_id,
    role_name="管理员",
    role_code="admin",
    description="系统管理员角色",
    level=1
)
role.set_audit_fields('admin_user_id')
session.add(role)

# 创建权限
permission = Permission(
    tenant_id=tenant.tenant_id,
    permission_name="用户管理",
    permission_code="user:manage",
    resource="user",
    action="manage",
    description="管理用户的权限"
)
permission.set_audit_fields('admin_user_id')
session.add(permission)

session.commit()
```

### 5. 创建关联关系

```python
from domain_common.models.iam_models import UserRole, RolePermission

# 用户角色关联
user_role = UserRole(
    tenant_id=tenant.tenant_id,
    user_id=user.user_id,
    role_id=role.role_id,
    assigned_by='admin_user_id'
)
session.add(user_role)

# 角色权限关联
role_permission = RolePermission(
    tenant_id=tenant.tenant_id,
    role_id=role.role_id,
    permission_id=permission.permission_id,
    assigned_by='admin_user_id'
)
session.add(role_permission)

session.commit()
```

### 6. 状态管理

```python
# 激活用户
user.activate('admin_user_id')

# 停用角色
role.deactivate('admin_user_id')

# 暂停权限
permission.suspend('admin_user_id')

session.commit()
```

### 7. 查询示例

```python
from sqlalchemy import select
from domain_common.models.iam_models import User, Role, UserRole

# 获取租户下的所有活跃用户
query = select(User).where(
    User.tenant_id == tenant.tenant_id,
    User.status == 'active',
    User.deleted_at.is_(None)
)
active_users = session.execute(query).scalars().all()

# 使用租户感知查询方法
query = select(User)
active_users = User.filter_active_by_tenant(query, tenant.tenant_id)
users = session.execute(active_users).scalars().all()

# 获取用户的所有角色
query = select(Role).join(
    UserRole, 
    (UserRole.role_id == Role.role_id) & 
    (UserRole.tenant_id == Role.tenant_id)
).where(
    UserRole.user_id == user.user_id,
    UserRole.tenant_id == tenant.tenant_id,
    UserRole.status == 'active',
    Role.deleted_at.is_(None)
)
user_roles = session.execute(query).scalars().all()
```

### 8. 审计日志记录

```python
from domain_common.models.iam_models import AuditLog

# 记录操作日志
audit_log = AuditLog(
    tenant_id=tenant.tenant_id,
    user_id='admin_user_id',
    operation_type='create',
    resource_type='user',
    resource_id=user.user_id,
    operation_name='创建用户',
    before_data=None,
    after_data={"user_id": user.user_id, "username": user.username},
    result='success'
)
session.add(audit_log)
session.commit()
```

## 注意事项

1. **多租户隔离**: 所有查询都应包含 `tenant_id` 过滤，推荐使用 `TenantAwareQueryMixin` 提供的方法
2. **软删除**: 使用 `deleted_at` 字段标记删除，不要物理删除数据
3. **审计追踪**: 重要操作应记录审计日志
4. **JSONB 字段**: 使用 `JSONBMixin` 提供的方法操作 JSONB 字段
5. **状态管理**: 使用 `IAMEntityBase` 提供的状态管理方法

## 最佳实践

1. **使用事务**: 所有修改操作应在事务中执行
2. **设置审计字段**: 使用 `set_audit_fields()` 方法设置审计字段
3. **租户感知查询**: 使用 `filter_by_tenant()` 等方法确保数据隔离
4. **状态转换**: 使用 `activate()`, `deactivate()` 等方法管理状态
5. **JSONB 操作**: 使用 `set_jsonb_value()` 等方法操作 JSONB 字段
