# IAM模型变更日志 - 用户组和平台角色功能

## 版本：v2.0.0
## 日期：2025-07-29
## 变更类型：功能增强

---

## 📋 变更概述

本次更新为IAM系统添加了用户组概念和平台/租户管理员角色区分功能，实现了更灵活的权限管理和组织结构映射。

## 🎯 解决的问题

### 1. 权限管理复杂性
- **问题**：直接为每个用户分配角色和权限，管理复杂度高
- **解决方案**：引入用户组概念，实现权限复用和批量管理

### 2. 组织结构映射缺失
- **问题**：无法有效映射现实中的部门、小组、项目组等组织结构
- **解决方案**：用户组支持多种类型和层级结构

### 3. 平台与租户权限混淆
- **问题**：缺乏平台级管理员和租户级管理员的明确区分
- **解决方案**：扩展角色模型，支持平台级角色

## 🔧 技术变更详情

### 1. 新增模型

#### UserGroup（用户组）
```python
class UserGroup(Base, TimestampMixin, AuditMixin):
    group_id: str           # 用户组ID
    tenant_id: str          # 租户ID
    group_name: str         # 用户组名称
    group_code: str         # 用户组编码
    group_type: str         # 用户组类型
    level: int              # 层级
    parent_group_id: str    # 父用户组ID
    max_members: int        # 最大成员数
    status: str             # 状态
    meta_data: dict         # 元数据
```

#### UserGroupMember（用户组成员关联）
```python
class UserGroupMember(Base, TimestampMixin, SoftDeleteMixin):
    user_id: str            # 用户ID
    group_id: str           # 用户组ID
    assignment_type: str    # 分配类型
    effective_date: date    # 生效日期
    expiry_date: date       # 过期日期
    status: str             # 状态
```

#### UserGroupRole（用户组角色关联）
```python
class UserGroupRole(Base, TimestampMixin, SoftDeleteMixin):
    group_id: str           # 用户组ID
    role_id: str            # 角色ID
    assignment_type: str    # 分配类型
    effective_date: date    # 生效日期
    expiry_date: date       # 过期日期
    status: str             # 状态
```

#### UserGroupPermission（用户组权限关联）
```python
class UserGroupPermission(Base, TimestampMixin, SoftDeleteMixin):
    group_id: str           # 用户组ID
    permission_id: str      # 权限ID
    assignment_type: str    # 分配类型
    effective_date: date    # 生效日期
    expiry_date: date       # 过期日期
    status: str             # 状态
```

### 2. 扩展现有模型

#### Role（角色扩展）
```python
# 新增字段
role_type: str              # 角色类型
is_platform_role: bool      # 是否为平台级角色
is_system_role: bool        # 是否为系统内置角色

# 修改字段
tenant_id: Optional[str]    # 租户ID（平台角色可为NULL）
```

### 3. 新增常量定义

#### RoleType（角色类型）
- `PLATFORM_SUPER_ADMIN`：平台超级管理员
- `PLATFORM_ADMIN`：平台管理员
- `TENANT_SUPER_ADMIN`：租户超级管理员
- `TENANT_ADMIN`：租户管理员
- `TENANT_USER`：租户普通用户
- `CUSTOM`：自定义角色

#### UserGroupType（用户组类型）
- `DEPARTMENT`：部门
- `TEAM`：小组/团队
- `PROJECT`：项目组
- `ROLE_GROUP`：角色组
- `PERMISSION_GROUP`：权限组
- `TEMPORARY`：临时组

#### AssignmentType（分配类型）
- `PERMANENT`：永久分配
- `TEMPORARY`：临时分配
- `INHERITED`：继承分配

## 📁 文件变更清单

### 核心模型文件
- ✅ `domain_common/models/constants.py` - 新增角色和用户组相关常量
- ✅ `domain_common/models/iam_models/core.py` - 扩展Role模型，新增UserGroup模型
- ✅ `domain_common/models/iam_models/relations.py` - 新增用户组关联表模型
- ✅ `domain_common/models/iam_models/__init__.py` - 更新模型导出

### 数据库迁移
- ✅ `domain_common/models/iam_models/migrations/add_user_groups_and_platform_roles.sql` - 数据库迁移脚本

### 示例和文档
- ✅ `domain_common/models/iam_models/examples/user_group_service_example.py` - 用户组服务示例
- ✅ `domain_common/models/iam_models/docs/USER_GROUPS_AND_PLATFORM_ROLES.md` - 功能说明文档
- ✅ `domain_common/models/iam_models/tests/test_user_groups_and_platform_roles.py` - 测试用例

## 🗄️ 数据库变更

### 表结构变更
1. **修改roles表**
   - 添加 `role_type` 字段
   - 添加 `is_platform_role` 字段
   - 添加 `is_system_role` 字段
   - 修改 `tenant_id` 允许NULL

2. **新增表**
   - `user_groups` - 用户组主表
   - `user_group_members` - 用户组成员关联表
   - `user_group_roles` - 用户组角色关联表
   - `user_group_permissions` - 用户组权限关联表

### 索引变更
- 新增角色类型相关索引
- 新增用户组相关索引
- 新增平台角色唯一约束

### 预置数据
- 插入平台超级管理员角色
- 插入平台管理员角色
- 为现有租户创建租户超级管理员角色

## 🔄 兼容性说明

### 向后兼容性
- ✅ 现有的用户、角色、权限模型保持不变
- ✅ 现有的UserRole、RolePermission关联保持不变
- ✅ 现有的API接口继续有效

### 数据迁移
- 现有角色自动设置为 `role_type = 'custom'`
- 现有角色的 `is_platform_role = false`
- 现有角色的 `is_system_role = false`

## 🚀 使用指南

### 1. 创建用户组
```python
user_group = await service.create_user_group(
    tenant_id="tenant_123",
    group_name="技术部",
    group_code="TECH_DEPT",
    group_type=UserGroupType.DEPARTMENT
)
```

### 2. 管理用户组成员
```python
# 添加用户到用户组
await service.add_user_to_group(
    tenant_id="tenant_123",
    user_id="user_123",
    group_id=user_group.group_id
)

# 为用户组分配角色
await service.assign_role_to_group(
    tenant_id="tenant_123",
    group_id=user_group.group_id,
    role_id="developer_role_id"
)
```

### 3. 权限计算
```python
# 获取用户有效权限（包括用户组继承）
permissions = await service.get_user_effective_permissions(
    tenant_id="tenant_123",
    user_id="user_123"
)
```

## ⚠️ 注意事项

### 1. 数据库迁移
- 执行迁移脚本前请备份数据库
- 迁移过程中会添加新字段和表，确保有足够的存储空间
- 建议在维护窗口期间执行迁移

### 2. 性能影响
- 权限计算可能涉及更多表关联，建议使用缓存
- 大型用户组的成员变更需要考虑批量操作
- 用户组层级不宜过深，建议限制在3-4层

### 3. 安全考虑
- 平台超级管理员权限需要严格控制
- 用户组权限变更需要审计日志
- 临时权限需要定期清理过期数据

## 🔮 后续计划

### Phase 2: 高级功能
- 动态权限策略（ABAC）
- 权限审计和分析
- 组织结构自动同步

### Phase 3: 性能优化
- 权限缓存优化
- 批量操作API
- 权限计算性能优化

## 📞 支持和反馈

如有问题或建议，请联系：
- 技术支持：<EMAIL>
- 文档反馈：<EMAIL>
- 功能建议：<EMAIL>

---

**变更负责人**：IAM团队  
**审核人**：架构委员会  
**发布日期**：2025-07-29
