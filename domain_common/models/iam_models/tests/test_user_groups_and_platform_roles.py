"""
用户组和平台角色功能测试

测试新增的用户组概念和平台/租户管理员角色区分功能
"""

import pytest
import uuid
from datetime import datetime, date
from sqlalchemy.ext.asyncio import AsyncSession

from domain_common.models import CommonStatus
from domain_common.models.constants import UserGroupType, AssignmentType, RoleType
from domain_common.models.iam_models import (
    Tenant, User, Role, Permission, UserGroup,
    UserRole, UserGroupMember, UserGroupRole, UserGroupPermission
)


class TestUserGroupModel:
    """用户组模型测试"""
    
    @pytest.fixture
    async def sample_tenant(self, session: AsyncSession):
        """创建测试租户"""
        tenant = Tenant(
            tenant_id=str(uuid.uuid4()),
            tenant_name="测试租户",
            tenant_code="TEST_TENANT",
            status=CommonStatus.ACTIVE
        )
        session.add(tenant)
        await session.commit()
        return tenant
    
    @pytest.fixture
    async def sample_user_group(self, session: AsyncSession, sample_tenant):
        """创建测试用户组"""
        user_group = UserGroup(
            group_id=str(uuid.uuid4()),
            tenant_id=sample_tenant.tenant_id,
            group_name="技术部",
            group_code="TECH_DEPT",
            group_type=UserGroupType.DEPARTMENT,
            description="技术部门用户组",
            status=CommonStatus.ACTIVE
        )
        session.add(user_group)
        await session.commit()
        return user_group
    
    async def test_create_user_group(self, session: AsyncSession, sample_tenant):
        """测试创建用户组"""
        user_group = UserGroup(
            group_id=str(uuid.uuid4()),
            tenant_id=sample_tenant.tenant_id,
            group_name="AI项目组",
            group_code="AI_PROJECT",
            group_type=UserGroupType.PROJECT,
            description="AI项目开发组",
            max_members=20,
            status=CommonStatus.ACTIVE
        )
        
        session.add(user_group)
        await session.commit()
        
        # 验证创建成功
        assert user_group.group_id is not None
        assert user_group.group_name == "AI项目组"
        assert user_group.group_type == UserGroupType.PROJECT
        assert user_group.max_members == 20
    
    async def test_user_group_hierarchy(self, session: AsyncSession, sample_user_group):
        """测试用户组层级关系"""
        # 创建子用户组
        child_group = UserGroup(
            group_id=str(uuid.uuid4()),
            tenant_id=sample_user_group.tenant_id,
            group_name="前端小组",
            group_code="FRONTEND_TEAM",
            group_type=UserGroupType.TEAM,
            parent_group_id=sample_user_group.group_id,
            level=2,
            status=CommonStatus.ACTIVE
        )
        
        session.add(child_group)
        await session.commit()
        
        # 验证层级关系
        assert child_group.parent_group_id == sample_user_group.group_id
        assert child_group.level == 2
        
        # 测试获取子组数量
        children_count = await UserGroup.get_group_children_count(
            session, sample_user_group.group_id
        )
        assert children_count == 1


class TestPlatformRoles:
    """平台角色测试"""
    
    async def test_create_platform_role(self, session: AsyncSession):
        """测试创建平台级角色"""
        platform_role = Role(
            role_id=str(uuid.uuid4()),
            tenant_id=None,  # 平台级角色
            role_name="平台超级管理员",
            role_code="PLATFORM_SUPER_ADMIN",
            role_type=RoleType.PLATFORM_SUPER_ADMIN,
            is_platform_role=True,
            is_system_role=True,
            level=1,
            status=CommonStatus.ACTIVE
        )
        
        session.add(platform_role)
        await session.commit()
        
        # 验证平台角色属性
        assert platform_role.tenant_id is None
        assert platform_role.is_platform_role is True
        assert platform_role.is_system_role is True
        assert platform_role.role_type == RoleType.PLATFORM_SUPER_ADMIN
    
    async def test_create_tenant_role(self, session: AsyncSession, sample_tenant):
        """测试创建租户级角色"""
        tenant_role = Role(
            role_id=str(uuid.uuid4()),
            tenant_id=sample_tenant.tenant_id,
            role_name="租户超级管理员",
            role_code="TENANT_SUPER_ADMIN",
            role_type=RoleType.TENANT_SUPER_ADMIN,
            is_platform_role=False,
            is_system_role=True,
            level=1,
            status=CommonStatus.ACTIVE
        )
        
        session.add(tenant_role)
        await session.commit()
        
        # 验证租户角色属性
        assert tenant_role.tenant_id == sample_tenant.tenant_id
        assert tenant_role.is_platform_role is False
        assert tenant_role.role_type == RoleType.TENANT_SUPER_ADMIN


class TestUserGroupRelations:
    """用户组关联关系测试"""
    
    @pytest.fixture
    async def sample_user(self, session: AsyncSession, sample_tenant):
        """创建测试用户"""
        user = User(
            user_id=str(uuid.uuid4()),
            tenant_id=sample_tenant.tenant_id,
            username="testuser",
            email="<EMAIL>",
            password_hash="hashed_password",
            status=CommonStatus.ACTIVE
        )
        session.add(user)
        await session.commit()
        return user
    
    @pytest.fixture
    async def sample_role(self, session: AsyncSession, sample_tenant):
        """创建测试角色"""
        role = Role(
            role_id=str(uuid.uuid4()),
            tenant_id=sample_tenant.tenant_id,
            role_name="开发者",
            role_code="DEVELOPER",
            role_type=RoleType.CUSTOM,
            status=CommonStatus.ACTIVE
        )
        session.add(role)
        await session.commit()
        return role
    
    async def test_user_group_member(
        self, 
        session: AsyncSession, 
        sample_user_group, 
        sample_user
    ):
        """测试用户组成员关联"""
        member = UserGroupMember(
            tenant_id=sample_user_group.tenant_id,
            user_id=sample_user.user_id,
            group_id=sample_user_group.group_id,
            assignment_type=AssignmentType.PERMANENT,
            status=CommonStatus.ACTIVE
        )
        
        session.add(member)
        await session.commit()
        
        # 验证成员关联
        assert member.user_id == sample_user.user_id
        assert member.group_id == sample_user_group.group_id
        assert member.assignment_type == AssignmentType.PERMANENT
    
    async def test_user_group_role(
        self, 
        session: AsyncSession, 
        sample_user_group, 
        sample_role
    ):
        """测试用户组角色关联"""
        group_role = UserGroupRole(
            tenant_id=sample_user_group.tenant_id,
            group_id=sample_user_group.group_id,
            role_id=sample_role.role_id,
            assignment_type=AssignmentType.PERMANENT,
            status=CommonStatus.ACTIVE
        )
        
        session.add(group_role)
        await session.commit()
        
        # 验证角色关联
        assert group_role.group_id == sample_user_group.group_id
        assert group_role.role_id == sample_role.role_id
    
    async def test_temporary_assignment(
        self, 
        session: AsyncSession, 
        sample_user_group, 
        sample_user
    ):
        """测试临时分配"""
        from datetime import timedelta
        
        # 创建临时成员分配
        expiry_date = date.today() + timedelta(days=30)
        member = UserGroupMember(
            tenant_id=sample_user_group.tenant_id,
            user_id=sample_user.user_id,
            group_id=sample_user_group.group_id,
            assignment_type=AssignmentType.TEMPORARY,
            effective_date=date.today(),
            expiry_date=expiry_date,
            status=CommonStatus.ACTIVE
        )
        
        session.add(member)
        await session.commit()
        
        # 验证临时分配
        assert member.assignment_type == AssignmentType.TEMPORARY
        assert member.expiry_date == expiry_date


class TestUserGroupService:
    """用户组服务测试"""
    
    async def test_user_group_creation_validation(self, session: AsyncSession):
        """测试用户组创建验证"""
        from domain_common.models.iam_models.examples.user_group_service_example import UserGroupService
        
        service = UserGroupService(session)
        
        # 测试无效的用户组类型
        with pytest.raises(ValueError, match="无效的用户组类型"):
            await service.create_user_group(
                tenant_id="test_tenant",
                group_name="测试组",
                group_code="TEST_GROUP",
                group_type="invalid_type"
            )
    
    async def test_max_members_limit(
        self, 
        session: AsyncSession, 
        sample_user_group, 
        sample_user
    ):
        """测试最大成员数限制"""
        from domain_common.models.iam_models.examples.user_group_service_example import UserGroupService
        
        # 设置最大成员数为1
        sample_user_group.max_members = 1
        await session.commit()
        
        service = UserGroupService(session)
        
        # 添加第一个用户（应该成功）
        await service.add_user_to_group(
            tenant_id=sample_user_group.tenant_id,
            user_id=sample_user.user_id,
            group_id=sample_user_group.group_id
        )
        
        # 尝试添加第二个用户（应该失败）
        second_user = User(
            user_id=str(uuid.uuid4()),
            tenant_id=sample_user_group.tenant_id,
            username="testuser2",
            email="<EMAIL>",
            password_hash="hashed_password",
            status=CommonStatus.ACTIVE
        )
        session.add(second_user)
        await session.commit()
        
        with pytest.raises(ValueError, match="用户组成员数已达到上限"):
            await service.add_user_to_group(
                tenant_id=sample_user_group.tenant_id,
                user_id=second_user.user_id,
                group_id=sample_user_group.group_id
            )


class TestPermissionInheritance:
    """权限继承测试"""
    
    async def test_effective_permissions_calculation(
        self, 
        session: AsyncSession, 
        sample_tenant,
        sample_user,
        sample_user_group,
        sample_role
    ):
        """测试有效权限计算"""
        from domain_common.models.iam_models.examples.user_group_service_example import UserGroupService
        
        # 创建权限
        permission = Permission(
            permission_id=str(uuid.uuid4()),
            tenant_id=sample_tenant.tenant_id,
            permission_name="代码读取",
            permission_code="code:read",
            resource="code",
            action="read",
            status=CommonStatus.ACTIVE
        )
        session.add(permission)
        
        # 将用户加入用户组
        member = UserGroupMember(
            tenant_id=sample_tenant.tenant_id,
            user_id=sample_user.user_id,
            group_id=sample_user_group.group_id,
            assignment_type=AssignmentType.PERMANENT,
            status=CommonStatus.ACTIVE
        )
        session.add(member)
        
        # 为用户组分配角色
        group_role = UserGroupRole(
            tenant_id=sample_tenant.tenant_id,
            group_id=sample_user_group.group_id,
            role_id=sample_role.role_id,
            assignment_type=AssignmentType.PERMANENT,
            status=CommonStatus.ACTIVE
        )
        session.add(group_role)
        
        await session.commit()
        
        # 计算有效权限
        service = UserGroupService(session)
        permissions = await service.get_user_effective_permissions(
            tenant_id=sample_tenant.tenant_id,
            user_id=sample_user.user_id
        )
        
        # 验证权限结构
        assert "direct_permissions" in permissions
        assert "role_permissions" in permissions
        assert "group_permissions" in permissions
        assert "group_role_permissions" in permissions


# 运行测试的配置
if __name__ == "__main__":
    pytest.main([__file__, "-v"])
