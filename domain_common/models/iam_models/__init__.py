"""
IAM 模型包

导出所有 IAM 相关的数据模型和工具类

架构说明：
1. 继承结构简化：
   - 所有模型直接继承 Base 基类
   - 使用便捷函数创建通用字段
   - 明确字段定义，提高可读性

2. 会话管理策略：
   - 活跃会话存储在 Redis 中（高性能，TTL支持）
   - UserSessionHistory 仅用于历史记录和审计
   - 验证码也建议存储在 Redis 中

3. 审计日志优化：
   - 使用 session_context 存储会话上下文信息
   - 避免对易失性 Redis 数据的直接依赖
   - 提供 AuditLogBuilder 简化审计日志创建

4. 工具类：
   - StatusManager: 状态管理
   - AuditManager: 审计管理
   - AuditLogBuilder: 审计日志构建器
"""

# 审计模型
from .audit import AuditLog, AuditLogBuilder  # 新增：审计日志构建器
# 认证安全模型
from .auth import UserSessionHistory  # 注意：已从UserSession重命名为UserSessionHistory
from .auth import PasswordHistory, UserMFA, VerificationCode
# 核心业务模型
from .core import Permission, Role, Tenant, User, UserGroup
# 策略模型
from .policies import PermissionPolicy
# 关联关系模型
from .relations import RolePermission, UserPolicy, UserRole, UserGroupMember, UserGroupRole, UserGroupPermission
# 系统模型
from .system import CacheKey, SystemConfig, TenantConfig, SecurityPolicy, SecurityEvent
# 任务模型
from .tasks import BatchTask
# RAG 特定模型
from .rag import Document, KnowledgeBase, KnowledgeBaseAccess

# 导出所有模型
__all__ = [
    # 核心业务模型
    "Tenant",
    "User",
    "Role",
    "Permission",
    "UserGroup",
    # 关联关系模型
    "UserRole",
    "RolePermission",
    "UserPolicy",
    "UserGroupMember",
    "UserGroupRole",
    "UserGroupPermission",
    # 认证安全模型
    "UserSessionHistory",  # 重命名：UserSession -> UserSessionHistory
    "UserMFA",
    "VerificationCode",
    "PasswordHistory",
    # 策略模型
    "PermissionPolicy",
    # 任务模型
    "BatchTask",
    # 系统模型
    "SystemConfig",
    "TenantConfig",
    "CacheKey",
    "SecurityPolicy",
    "SecurityEvent",
    # 审计模型
    "AuditLog",
    "AuditLogBuilder",  # 新增
    # RAG 特定模型
    "KnowledgeBase",
    "Document",
    "KnowledgeBaseAccess",
]
