"""
领域模型模块初始化文件。

此模块重新导出领域模型中的重要类和函数，
便于其他模块使用。
"""

# 基础模型
from .base_model import Base

# 常量和枚举
from .constants import (
    CommonStatus,
    JSONType,
    FieldLengths,
    DefaultValues,
    SchemaConstants
)

# 字段工厂和工具
from .fields import Fields

# 混入类和管理器
from .mixins import (
    SoftDeleteMixin,
    TenantMixin,
    AuditMixin,
    StatusMixin,
    FullAuditMixin,
    StatusManager,
    AuditManager
)

__all__ = [
    # 基础类
    "Base",
    
    # 常量
    "CommonStatus",
    "JSONType", 
    "FieldLengths",
    "DefaultValues",
    "SchemaConstants",
    
    # 字段工厂
    "Fields",

    # 混入类
    "SoftDeleteMixin",
    "TenantMixin",
    "AuditMixin", 
    "StatusMixin",
    "FullAuditMixin",
    
    # 管理器
    "StatusManager",
    "AuditManager",
]
