"""
会话管理器

负责用户会话的创建、验证、续期、销毁等操作
"""

import uuid
import json
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict

from commonlib.storages.persistence.redis.repository import RedisRepository


@dataclass
class SessionInfo:
    """会话信息"""
    session_id: str
    user_id: str
    tenant_id: str
    device_fingerprint: Optional[str]
    ip_address: Optional[str]
    user_agent: Optional[str]
    login_time: datetime
    last_activity: datetime
    expires_at: datetime
    is_active: bool = True


@dataclass
class DeviceInfo:
    """设备信息"""
    fingerprint: str
    user_agent: str
    ip_address: str
    location: Optional[str] = None
    is_trusted: bool = False


class SessionManager:
    """会话管理器"""
    
    def __init__(
        self,
        redis_repo: RedisRepository,
        session_timeout_minutes: int = 120,
        max_concurrent_sessions: int = 5,
        enable_device_tracking: bool = True
    ):
        self.redis_repo = redis_repo
        self.session_timeout_minutes = session_timeout_minutes
        self.max_concurrent_sessions = max_concurrent_sessions
        self.enable_device_tracking = enable_device_tracking
    
    async def create_session(
        self,
        user_id: str,
        tenant_id: str,
        device_fingerprint: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        remember_me: bool = False
    ) -> SessionInfo:
        """创建新会话"""
        # TODO: 实现会话创建逻辑
        # 1. 生成唯一会话ID
        # 2. 检查并发会话限制
        # 3. 创建会话信息
        # 4. 存储会话到Redis
        # 5. 更新用户会话列表
        # 6. 记录登录日志
        # 7. 返回会话信息
        
        session_id = str(uuid.uuid4())
        now = datetime.now()
        
        # 计算会话过期时间
        if remember_me:
            expires_at = now + timedelta(days=30)  # 记住我：30天
        else:
            expires_at = now + timedelta(minutes=self.session_timeout_minutes)
        
        # 检查并发会话限制
        await self._enforce_concurrent_session_limit(user_id)
        
        # 创建会话信息
        session_info = SessionInfo(
            session_id=session_id,
            user_id=user_id,
            tenant_id=tenant_id,
            device_fingerprint=device_fingerprint,
            ip_address=ip_address,
            user_agent=user_agent,
            login_time=now,
            last_activity=now,
            expires_at=expires_at
        )
        
        # 存储会话
        await self._store_session(session_info)
        
        # 更新用户会话列表
        await self._add_user_session(user_id, session_id)
        
        # 设备跟踪
        if self.enable_device_tracking and device_fingerprint:
            await self._track_device(user_id, device_fingerprint, ip_address, user_agent)
        
        return session_info
    
    async def get_session(self, session_id: str) -> Optional[SessionInfo]:
        """获取会话信息"""
        # TODO: 实现会话获取逻辑
        # 1. 从Redis获取会话数据
        # 2. 检查会话是否过期
        # 3. 验证会话有效性
        # 4. 更新最后活动时间
        # 5. 返回会话信息
        
        session_data = await self.redis_repo.get(f"session:{session_id}")
        if not session_data:
            return None
        
        # 反序列化会话信息
        session_info = self._deserialize_session(session_data)
        
        # 检查会话是否过期
        if datetime.now() > session_info.expires_at:
            await self.destroy_session(session_id)
            return None
        
        # 更新最后活动时间
        await self._update_last_activity(session_id)
        
        return session_info
    
    async def validate_session(
        self,
        session_id: str,
        device_fingerprint: Optional[str] = None,
        ip_address: Optional[str] = None
    ) -> bool:
        """验证会话有效性"""
        # TODO: 实现会话验证逻辑
        # 1. 获取会话信息
        # 2. 检查会话状态
        # 3. 验证设备指纹
        # 4. 检查IP地址变化
        # 5. 检测异常登录
        # 6. 返回验证结果
        
        session_info = await self.get_session(session_id)
        if not session_info or not session_info.is_active:
            return False
        
        # 验证设备指纹
        if (self.enable_device_tracking and 
            device_fingerprint and 
            session_info.device_fingerprint and
            device_fingerprint != session_info.device_fingerprint):
            # 设备指纹不匹配，可能是会话劫持
            await self._handle_suspicious_activity(session_id, "device_mismatch")
            return False
        
        # 检查IP地址变化
        if ip_address and session_info.ip_address:
            if await self._is_suspicious_ip_change(session_info.ip_address, ip_address):
                await self._handle_suspicious_activity(session_id, "ip_change")
                return False
        
        return True
    
    async def extend_session(self, session_id: str, extend_minutes: int = None) -> bool:
        """延长会话有效期"""
        # TODO: 实现会话延期逻辑
        # 1. 获取会话信息
        # 2. 检查是否允许延期
        # 3. 计算新的过期时间
        # 4. 更新会话信息
        # 5. 记录延期日志
        # 6. 返回延期结果
        
        session_info = await self.get_session(session_id)
        if not session_info:
            return False
        
        # 计算新的过期时间
        if extend_minutes is None:
            extend_minutes = self.session_timeout_minutes
        
        new_expires_at = datetime.now() + timedelta(minutes=extend_minutes)
        session_info.expires_at = new_expires_at
        
        # 更新会话
        await self._store_session(session_info)
        
        return True
    
    async def destroy_session(self, session_id: str) -> bool:
        """销毁会话"""
        # TODO: 实现会话销毁逻辑
        # 1. 获取会话信息
        # 2. 从Redis删除会话
        # 3. 从用户会话列表移除
        # 4. 撤销相关令牌
        # 5. 记录登出日志
        # 6. 返回销毁结果
        
        session_info = await self.get_session(session_id)
        if not session_info:
            return False
        
        # 删除会话
        await self.redis_repo.delete(f"session:{session_id}")
        
        # 从用户会话列表移除
        await self._remove_user_session(session_info.user_id, session_id)
        
        return True
    
    async def destroy_user_sessions(
        self,
        user_id: str,
        exclude_session_id: Optional[str] = None
    ) -> int:
        """销毁用户的所有会话"""
        # TODO: 实现用户会话批量销毁逻辑
        # 1. 获取用户所有会话
        # 2. 排除指定会话（如当前会话）
        # 3. 批量删除会话
        # 4. 清理用户会话列表
        # 5. 记录批量登出日志
        # 6. 返回销毁数量
        
        user_sessions = await self._get_user_sessions(user_id)
        destroyed_count = 0
        
        for session_id in user_sessions:
            if session_id != exclude_session_id:
                if await self.destroy_session(session_id):
                    destroyed_count += 1
        
        return destroyed_count
    
    async def get_user_active_sessions(self, user_id: str) -> List[SessionInfo]:
        """获取用户活跃会话列表"""
        # TODO: 实现用户活跃会话查询逻辑
        # 1. 获取用户会话列表
        # 2. 批量查询会话信息
        # 3. 过滤过期和无效会话
        # 4. 按最后活动时间排序
        # 5. 返回活跃会话列表
        
        user_sessions = await self._get_user_sessions(user_id)
        active_sessions = []
        
        for session_id in user_sessions:
            session_info = await self.get_session(session_id)
            if session_info and session_info.is_active:
                active_sessions.append(session_info)
        
        # 按最后活动时间排序
        active_sessions.sort(key=lambda x: x.last_activity, reverse=True)
        
        return active_sessions
    
    async def cleanup_expired_sessions(self) -> int:
        """清理过期会话"""
        # TODO: 实现过期会话清理逻辑
        # 1. 扫描所有会话
        # 2. 识别过期会话
        # 3. 批量删除过期会话
        # 4. 清理相关数据
        # 5. 记录清理日志
        # 6. 返回清理数量
        
        # 这里需要实现批量扫描和清理逻辑
        # 可以使用Redis的SCAN命令来遍历会话
        return 0
    
    async def _store_session(self, session_info: SessionInfo) -> None:
        """存储会话信息"""
        session_data = self._serialize_session(session_info)
        ttl = int((session_info.expires_at - datetime.now()).total_seconds())
        
        await self.redis_repo.set(
            f"session:{session_info.session_id}",
            session_data,
            ttl=max(ttl, 60)  # 最少保留60秒
        )
    
    async def _add_user_session(self, user_id: str, session_id: str) -> None:
        """添加用户会话"""
        await self.redis_repo.sadd(f"user_sessions:{user_id}", session_id)
    
    async def _remove_user_session(self, user_id: str, session_id: str) -> None:
        """移除用户会话"""
        await self.redis_repo.srem(f"user_sessions:{user_id}", session_id)
    
    async def _get_user_sessions(self, user_id: str) -> List[str]:
        """获取用户会话列表"""
        sessions = await self.redis_repo.smembers(f"user_sessions:{user_id}")
        return list(sessions) if sessions else []
    
    async def _update_last_activity(self, session_id: str) -> None:
        """更新最后活动时间"""
        session_data = await self.redis_repo.get(f"session:{session_id}")
        if session_data:
            session_info = self._deserialize_session(session_data)
            session_info.last_activity = datetime.now()
            await self._store_session(session_info)
    
    async def _enforce_concurrent_session_limit(self, user_id: str) -> None:
        """强制执行并发会话限制"""
        user_sessions = await self._get_user_sessions(user_id)
        
        if len(user_sessions) >= self.max_concurrent_sessions:
            # 获取所有会话的最后活动时间，删除最旧的会话
            sessions_with_activity = []
            for session_id in user_sessions:
                session_info = await self.get_session(session_id)
                if session_info:
                    sessions_with_activity.append((session_id, session_info.last_activity))
            
            # 按最后活动时间排序，删除最旧的会话
            sessions_with_activity.sort(key=lambda x: x[1])
            sessions_to_remove = len(sessions_with_activity) - self.max_concurrent_sessions + 1
            
            for i in range(sessions_to_remove):
                await self.destroy_session(sessions_with_activity[i][0])
    
    async def _track_device(
        self,
        user_id: str,
        device_fingerprint: str,
        ip_address: Optional[str],
        user_agent: Optional[str]
    ) -> None:
        """跟踪设备信息"""
        device_info = DeviceInfo(
            fingerprint=device_fingerprint,
            user_agent=user_agent or "",
            ip_address=ip_address or "",
            location=None,  # 可以集成IP地理位置服务
            is_trusted=False
        )
        
        await self.redis_repo.set(
            f"device:{user_id}:{device_fingerprint}",
            asdict(device_info),
            ttl=30 * 24 * 3600  # 30天
        )
    
    async def _is_suspicious_ip_change(self, old_ip: str, new_ip: str) -> bool:
        """检查IP地址变化是否可疑"""
        # TODO: 实现IP变化检测逻辑
        # 1. 比较IP地址段
        # 2. 检查地理位置变化
        # 3. 分析变化频率
        # 4. 返回是否可疑
        
        # 简单实现：如果IP完全不同则认为可疑
        return old_ip != new_ip
    
    async def _handle_suspicious_activity(self, session_id: str, activity_type: str) -> None:
        """处理可疑活动"""
        # TODO: 实现可疑活动处理逻辑
        # 1. 记录安全事件
        # 2. 发送安全告警
        # 3. 可选择性销毁会话
        # 4. 通知用户
        
        # 记录安全事件
        await self.redis_repo.lpush(
            f"security_events:{session_id}",
            {
                "type": activity_type,
                "timestamp": datetime.now().isoformat(),
                "session_id": session_id
            }
        )
    
    def _serialize_session(self, session_info: SessionInfo) -> Dict[str, Any]:
        """序列化会话信息"""
        data = asdict(session_info)
        # 转换datetime为ISO格式字符串
        for key in ['login_time', 'last_activity', 'expires_at']:
            if isinstance(data[key], datetime):
                data[key] = data[key].isoformat()
        return data
    
    def _deserialize_session(self, session_data: Dict[str, Any]) -> SessionInfo:
        """反序列化会话信息"""
        # 转换ISO格式字符串为datetime
        for key in ['login_time', 'last_activity', 'expires_at']:
            if isinstance(session_data[key], str):
                session_data[key] = datetime.fromisoformat(session_data[key])
        
        return SessionInfo(**session_data)
