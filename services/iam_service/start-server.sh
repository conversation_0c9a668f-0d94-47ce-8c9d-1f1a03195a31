#!/bin/bash

# IAM 服务启动脚本

set -e

echo "启动 IAM 服务..."

# 检查环境变量
if [ -z "$DATABASE_URL" ]; then
    echo "警告: DATABASE_URL 环境变量未设置"
fi

if [ -z "$REDIS_URL" ]; then
    echo "警告: REDIS_URL 环境变量未设置"
fi

# 等待数据库就绪
echo "等待数据库连接..."
python -c "
import time
import sys
from sqlalchemy import create_engine
from sqlalchemy.exc import OperationalError

db_url = '$DATABASE_URL'
if not db_url:
    print('数据库 URL 未配置，跳过连接检查')
    sys.exit(0)

max_retries = 30
for i in range(max_retries):
    try:
        engine = create_engine(db_url)
        engine.connect()
        print('数据库连接成功')
        break
    except OperationalError:
        if i == max_retries - 1:
            print('数据库连接失败')
            sys.exit(1)
        print(f'等待数据库... ({i+1}/{max_retries})')
        time.sleep(2)
"

# 运行数据库迁移
echo "运行数据库迁移..."
# alembic upgrade head

# 启动服务
echo "启动 IAM 服务..."
exec python main.py
