from commonlib.core.containers.infra_container import InfraContainer
from commonlib.core.logging.tsif_logging import app_logger
from commonlib.utils.scheduler_tasks_context import scheduler_tasks_mgm
from dependency_injector.wiring import inject
from domain_common.interface.infra_redis.rd_decorator import (
    redis_cache_decorator, redis_distributed_task_preemption_decorator)


# Example 1: Using the simplified decorator from InfraContainer
@scheduler_tasks_mgm.add(trigger="interval", minutes=5)
@InfraContainer.with_distributed_lock(name="example_cached_task", lock_ttl_ms=10000)
@InfraContainer.with_cache(ttl_seconds=300, key_prefix="example")
@inject
async def example_cached_task(param1: str = "default"):
    """Example task that uses both distributed lock and cache decorators.

    This task will only run on one instance at a time and its results will be cached.
    """
    app_logger.info(f"Running example_cached_task with param: {param1}")
    return {"result": f"Processed {param1}", "timestamp": "2023-01-01"}


# Example 2: Using the original decorators directly
@scheduler_tasks_mgm.add(trigger="interval", minutes=10)
@redis_distributed_task_preemption_decorator(name="example_direct_task", px=15000)
@redis_cache_decorator(ttl=600, key_prefix="direct_example", need_hash=True)
@inject
async def example_direct_task():
    """Example task that uses the original decorators directly."""
    app_logger.info("Running example_direct_task")
    return {"status": "completed"}


# Example 3: Using rate limiting
@InfraContainer.with_rate_limit(tokens_per_second=1.0, burst=5, timeout=30)
@inject
async def rate_limited_api_call(url: str):
    """Example function that is rate limited to 1 call per second with a burst of 5."""
    app_logger.info(f"Making API call to {url}")
    # Simulated API call
    return {"url": url, "status": "success"}


# Example of how to use the rate limited function
@scheduler_tasks_mgm.add(trigger="interval", minutes=1)
@InfraContainer.with_distributed_lock(name="api_caller")
@inject
async def api_caller_task():
    """Task that calls the rate limited API function."""
    urls = [f"https://example.com/api/{i}" for i in range(10)]
    results = []

    for url in urls:
        # This will be rate limited to 1 call per second
        result = await rate_limited_api_call(url)
        results.append(result)

    app_logger.info(f"Completed {len(results)} API calls")
    return results
