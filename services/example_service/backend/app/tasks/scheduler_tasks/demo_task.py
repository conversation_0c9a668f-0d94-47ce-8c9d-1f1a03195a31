from commonlib.utils.scheduler_tasks_context import scheduler_tasks_mgm
from dependency_injector.wiring import inject
from domain_common.interface.infra_redis.rd_decorator import \
    redis_distributed_task_preemption_decorator


@scheduler_tasks_mgm.add(trigger="interval", seconds=10)
@redis_distributed_task_preemption_decorator(px=5000)
@inject
async def example_hello_task():
    print("hello world")
