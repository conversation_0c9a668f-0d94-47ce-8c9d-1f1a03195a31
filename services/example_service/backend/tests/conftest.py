import pytest
from backend.app.core.container import ServiceContainer
from backend.app.tasks.scheduler_mgm import dispatch_scheduler
from commonlib.core.containers.config_container import set_up_config_di
from commonlib.core.containers.infra_container import InfraContainer
from domain_common.app_builder.default_app_factory import AppInitializer


@pytest.fixture(scope="session")
def fastapi_app():
    """
    构建完整 FastAPI app（生产配置），用于测试。
    可在这里切换 mock 配置或 mock service container。
    """
    config = set_up_config_di()
    infra = InfraContainer(config=config)
    services = ServiceContainer(config=config, infra=infra)

    scheduler_modules = dispatch_scheduler()
    app = AppInitializer(
        config=config, infra=infra, services=services, wire_modules=scheduler_modules
    ).create_app()

    return app


@pytest.fixture()
def client(fastapi_app):
    """
    FastAPI 测试客户端
    """
    from fastapi.testclient import TestClient

    return TestClient(fastapi_app)
