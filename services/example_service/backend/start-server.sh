#!/usr/bin/env bash
set -euo pipefail  # 更严格的错误检查

# 环境变量配置（带默认值）
export APP_MODULE=${APP_MODULE:-"example_service.app.main:app"}  # FastAPI/Starlette 入口
export HOST=${HOST:-0.0.0.0}
export PORT=${PORT:-8000}
export ENVIRONMENT=${ENVIRONMENT:-production}
export LOG_LEVEL=${LOG_LEVEL:-info}

# 根据环境选择启动方式
if [[ "$ENVIRONMENT" == "development" ]]; then
    export WORKERS=2
    echo "🚀 启动开发服务 (Live Reload)"
    exec uvicorn "$APP_MODULE" \
        --reload \
        --reload-dir /app  \
        --host "$HOST" \
        --port "$PORT" \
        --log-level "$LOG_LEVEL"

elif [[ "$ENVIRONMENT" == "production" ]]; then
    export WORKERS=4
    echo "🚀 启动生产服务 ($WORKERS workers)"

    exec gunicorn \
        -k uvicorn.workers.UvicornWorker \
        -w "$WORKERS" \
        -b "$HOST:$PORT" \
        --access-logfile - \
        --error-logfile - \
        --timeout 120 \
        "$APP_MODULE"
else
    echo "❌ 未知环境: $ENVIRONMENT"
    exit 1
fi