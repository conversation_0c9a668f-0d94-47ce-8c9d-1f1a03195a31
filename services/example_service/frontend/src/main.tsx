/**
 * @file 应用主入口文件
 * @description React应用程序的主入口点，负责应用的初始化和挂载
 * @status 框架文件 - 完成
 */

import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App'
import './styles/index.css'
import { startMockWorker } from './mocks/browser'

// 开发环境下启用 MSW
if (import.meta.env.DEV) {
  startMockWorker()
}

// 注册 Service Worker (PWA)
if ('serviceWorker' in navigator && import.meta.env.PROD) {
  window.addEventListener('load', () => {
    navigator.serviceWorker.register('/sw.js')
      .then((registration) => {
        console.log('SW registered: ', registration)
      })
      .catch((registrationError) => {
        console.log('SW registration failed: ', registrationError)
      })
  })
}

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)