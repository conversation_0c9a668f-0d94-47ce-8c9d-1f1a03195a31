/**
 * @file CacheManager 测试
 * @description 测试缓存管理器的各种功能
 * @status 框架文件 - 完成
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { CacheManager, cached } from '../CacheManager'

describe('CacheManager', () => {
  let cache: CacheManager<string>

  beforeEach(() => {
    cache = new CacheManager<string>({
      ttl: 1000, // 1秒
      maxSize: 3,
      storage: 'memory',
    })
  })

  it('sets and gets values', () => {
    cache.set('key1', 'value1')
    expect(cache.get('key1')).toBe('value1')
  })

  it('returns null for non-existent keys', () => {
    expect(cache.get('nonexistent')).toBeNull()
  })

  it('handles expiration', async () => {
    cache.set('key1', 'value1', 100) // 100ms TTL
    expect(cache.get('key1')).toBe('value1')

    // 等待过期
    await new Promise(resolve => setTimeout(resolve, 150))
    expect(cache.get('key1')).toBeNull()
  })

  it('respects max size limit', () => {
    cache.set('key1', 'value1')
    cache.set('key2', 'value2')
    cache.set('key3', 'value3')
    cache.set('key4', 'value4') // 应该驱逐最旧的

    expect(cache.size()).toBe(3)
    expect(cache.get('key1')).toBeNull() // 最旧的应该被驱逐
    expect(cache.get('key4')).toBe('value4')
  })

  it('deletes values', () => {
    cache.set('key1', 'value1')
    expect(cache.has('key1')).toBe(true)
    
    cache.delete('key1')
    expect(cache.has('key1')).toBe(false)
  })

  it('clears all values', () => {
    cache.set('key1', 'value1')
    cache.set('key2', 'value2')
    expect(cache.size()).toBe(2)

    cache.clear()
    expect(cache.size()).toBe(0)
  })

  it('returns correct keys', () => {
    cache.set('key1', 'value1')
    cache.set('key2', 'value2')
    
    const keys = cache.keys()
    expect(keys).toContain('key1')
    expect(keys).toContain('key2')
    expect(keys).toHaveLength(2)
  })

  it('cleans up expired entries', async () => {
    cache.set('key1', 'value1', 100) // 100ms TTL
    cache.set('key2', 'value2', 1000) // 1s TTL
    
    expect(cache.size()).toBe(2)
    
    // 等待第一个过期
    await new Promise(resolve => setTimeout(resolve, 150))
    
    cache.cleanup()
    expect(cache.size()).toBe(1)
    expect(cache.has('key2')).toBe(true)
  })

  it('provides correct stats', () => {
    cache.set('key1', 'value1')
    cache.set('key2', 'value2')
    
    const stats = cache.getStats()
    expect(stats.total).toBe(2)
    expect(stats.valid).toBe(2)
    expect(stats.expired).toBe(0)
    expect(stats.maxSize).toBe(3)
    expect(stats.storage).toBe('memory')
  })
})

describe('cached decorator', () => {
  it('caches function results', () => {
    const mockFn = vi.fn((x: number) => x * 2)
    const cachedFn = cached(mockFn)

    // 第一次调用
    expect(cachedFn(5)).toBe(10)
    expect(mockFn).toHaveBeenCalledTimes(1)

    // 第二次调用应该使用缓存
    expect(cachedFn(5)).toBe(10)
    expect(mockFn).toHaveBeenCalledTimes(1)

    // 不同参数应该重新计算
    expect(cachedFn(3)).toBe(6)
    expect(mockFn).toHaveBeenCalledTimes(2)
  })

  it('uses custom key generator', () => {
    const mockFn = vi.fn((obj: { id: number }) => obj.id * 2)
    const cachedFn = cached(mockFn, {
      keyGenerator: (obj) => `id_${obj.id}`
    })

    const obj1 = { id: 1 }
    const obj2 = { id: 1 } // 不同对象但相同 id

    expect(cachedFn(obj1)).toBe(2)
    expect(mockFn).toHaveBeenCalledTimes(1)

    // 应该使用缓存，因为 id 相同
    expect(cachedFn(obj2)).toBe(2)
    expect(mockFn).toHaveBeenCalledTimes(1)
  })
})
