/**
 * @file 缓存管理器
 * @description 提供统一的缓存管理功能，支持内存缓存、localStorage 和 sessionStorage
 * @status 框架文件 - 完成
 */

// 缓存配置接口
export interface CacheConfig {
  ttl?: number // 生存时间（毫秒）
  maxSize?: number // 最大缓存条目数
  storage?: 'memory' | 'localStorage' | 'sessionStorage'
  serialize?: boolean // 是否序列化存储
}

// 缓存条目接口
interface CacheEntry<T> {
  value: T
  timestamp: number
  ttl?: number
}

// 缓存管理器类
export class CacheManager<T = any> {
  private cache = new Map<string, CacheEntry<T>>()
  private config: Required<CacheConfig>

  constructor(config: CacheConfig = {}) {
    this.config = {
      ttl: config.ttl || 5 * 60 * 1000, // 默认5分钟
      maxSize: config.maxSize || 100,
      storage: config.storage || 'memory',
      serialize: config.serialize ?? true,
    }

    // 如果使用浏览器存储，从存储中恢复缓存
    if (this.config.storage !== 'memory' && typeof window !== 'undefined') {
      this.loadFromStorage()
    }
  }

  /**
   * 设置缓存
   */
  set(key: string, value: T, ttl?: number): void {
    const entry: CacheEntry<T> = {
      value,
      timestamp: Date.now(),
      ttl: ttl || this.config.ttl,
    }

    // 检查缓存大小限制
    if (this.cache.size >= this.config.maxSize) {
      this.evictOldest()
    }

    this.cache.set(key, entry)

    // 同步到浏览器存储
    if (this.config.storage !== 'memory') {
      this.saveToStorage(key, entry)
    }
  }

  /**
   * 获取缓存
   */
  get(key: string): T | null {
    const entry = this.cache.get(key)

    if (!entry) {
      return null
    }

    // 检查是否过期
    if (this.isExpired(entry)) {
      this.delete(key)
      return null
    }

    return entry.value
  }

  /**
   * 删除缓存
   */
  delete(key: string): boolean {
    const deleted = this.cache.delete(key)

    // 从浏览器存储中删除
    if (this.config.storage !== 'memory' && typeof window !== 'undefined') {
      const storage = this.getStorage()
      storage?.removeItem(this.getStorageKey(key))
    }

    return deleted
  }

  /**
   * 清空缓存
   */
  clear(): void {
    this.cache.clear()

    // 清空浏览器存储
    if (this.config.storage !== 'memory' && typeof window !== 'undefined') {
      const storage = this.getStorage()
      if (storage) {
        const keys = Object.keys(storage).filter(key => 
          key.startsWith(this.getStoragePrefix())
        )
        keys.forEach(key => storage.removeItem(key))
      }
    }
  }

  /**
   * 检查缓存是否存在且未过期
   */
  has(key: string): boolean {
    return this.get(key) !== null
  }

  /**
   * 获取缓存大小
   */
  size(): number {
    return this.cache.size
  }

  /**
   * 获取所有缓存键
   */
  keys(): string[] {
    return Array.from(this.cache.keys())
  }

  /**
   * 清理过期缓存
   */
  cleanup(): void {
    const now = Date.now()
    const expiredKeys: string[] = []

    for (const [key, entry] of this.cache.entries()) {
      if (this.isExpired(entry)) {
        expiredKeys.push(key)
      }
    }

    expiredKeys.forEach(key => this.delete(key))
  }

  /**
   * 获取缓存统计信息
   */
  getStats() {
    const now = Date.now()
    let expired = 0
    let valid = 0

    for (const entry of this.cache.values()) {
      if (this.isExpired(entry)) {
        expired++
      } else {
        valid++
      }
    }

    return {
      total: this.cache.size,
      valid,
      expired,
      maxSize: this.config.maxSize,
      storage: this.config.storage,
    }
  }

  // 私有方法

  private isExpired(entry: CacheEntry<T>): boolean {
    if (!entry.ttl) return false
    return Date.now() - entry.timestamp > entry.ttl
  }

  private evictOldest(): void {
    let oldestKey: string | null = null
    let oldestTime = Date.now()

    for (const [key, entry] of this.cache.entries()) {
      if (entry.timestamp < oldestTime) {
        oldestTime = entry.timestamp
        oldestKey = key
      }
    }

    if (oldestKey) {
      this.delete(oldestKey)
    }
  }

  private getStorage(): Storage | null {
    if (typeof window === 'undefined') return null
    
    switch (this.config.storage) {
      case 'localStorage':
        return window.localStorage
      case 'sessionStorage':
        return window.sessionStorage
      default:
        return null
    }
  }

  private getStoragePrefix(): string {
    return 'cache_'
  }

  private getStorageKey(key: string): string {
    return `${this.getStoragePrefix()}${key}`
  }

  private saveToStorage(key: string, entry: CacheEntry<T>): void {
    const storage = this.getStorage()
    if (!storage) return

    try {
      const data = this.config.serialize ? JSON.stringify(entry) : entry
      storage.setItem(this.getStorageKey(key), data as string)
    } catch (error) {
      console.warn('Failed to save cache to storage:', error)
    }
  }

  private loadFromStorage(): void {
    const storage = this.getStorage()
    if (!storage) return

    try {
      const keys = Object.keys(storage).filter(key => 
        key.startsWith(this.getStoragePrefix())
      )

      for (const storageKey of keys) {
        const key = storageKey.replace(this.getStoragePrefix(), '')
        const data = storage.getItem(storageKey)
        
        if (data) {
          const entry = this.config.serialize ? JSON.parse(data) : data
          
          // 检查是否过期
          if (!this.isExpired(entry)) {
            this.cache.set(key, entry)
          } else {
            storage.removeItem(storageKey)
          }
        }
      }
    } catch (error) {
      console.warn('Failed to load cache from storage:', error)
    }
  }
}

// 创建默认缓存实例
export const defaultCache = new CacheManager()

// 创建 API 缓存实例
export const apiCache = new CacheManager({
  ttl: 10 * 60 * 1000, // 10分钟
  maxSize: 50,
  storage: 'sessionStorage',
})

// 创建用户数据缓存实例
export const userCache = new CacheManager({
  ttl: 30 * 60 * 1000, // 30分钟
  maxSize: 20,
  storage: 'localStorage',
})

// 缓存装饰器（用于函数结果缓存）
export function cached<T extends (...args: any[]) => any>(
  fn: T,
  options: {
    cache?: CacheManager
    keyGenerator?: (...args: Parameters<T>) => string
    ttl?: number
  } = {}
): T {
  const cache = options.cache || defaultCache
  const keyGenerator = options.keyGenerator || ((...args) => JSON.stringify(args))

  return ((...args: Parameters<T>) => {
    const key = `fn_${fn.name}_${keyGenerator(...args)}`
    
    // 尝试从缓存获取
    const cached = cache.get(key)
    if (cached !== null) {
      return cached
    }

    // 执行函数并缓存结果
    const result = fn(...args)
    cache.set(key, result, options.ttl)
    
    return result
  }) as T
}
