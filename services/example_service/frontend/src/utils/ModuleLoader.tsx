/**
 * @file 模块加载器
 * @description 动态加载和管理应用模块的核心系统
 * @status 框架文件 - 完成
 */

import React, { Suspense, lazy, ComponentType } from 'react'
import { RouteObject } from 'react-router-dom'

// 模块配置接口
export interface ModuleConfig {
  id: string
  name: string
  version: string
  description?: string
  routes?: RouteObject[]
  dependencies?: string[]
  permissions?: string[]
  enabled?: boolean
  lazy?: boolean
}

// 模块接口
export interface Module {
  config: ModuleConfig
  component?: ComponentType<any>
  routes?: RouteObject[]
  initialize?: () => Promise<void> | void
  destroy?: () => Promise<void> | void
}

// 模块注册表
class ModuleRegistry {
  private modules = new Map<string, Module>()
  private loadedModules = new Set<string>()
  private loadingModules = new Set<string>()

  /**
   * 注册模块
   */
  register(module: Module): void {
    const { id } = module.config

    if (this.modules.has(id)) {
      // 在开发环境下，由于 React StrictMode 可能导致重复注册，只显示调试信息
      if (import.meta.env.DEV) {
        console.debug(`Module ${id} is already registered (this is normal in development mode)`)
      } else {
        console.warn(`Module ${id} is already registered`)
      }
      return
    }

    this.modules.set(id, module)
    console.log(`Module ${id} registered successfully`)
  }

  /**
   * 获取模块
   */
  get(id: string): Module | undefined {
    return this.modules.get(id)
  }

  /**
   * 获取所有模块
   */
  getAll(): Module[] {
    return Array.from(this.modules.values())
  }

  /**
   * 获取启用的模块
   */
  getEnabled(): Module[] {
    return this.getAll().filter(module => module.config.enabled !== false)
  }

  /**
   * 加载模块
   */
  async load(id: string): Promise<Module | null> {
    if (this.loadedModules.has(id)) {
      return this.modules.get(id) || null
    }

    if (this.loadingModules.has(id)) {
      // 等待加载完成
      return new Promise((resolve) => {
        const checkLoaded = () => {
          if (this.loadedModules.has(id)) {
            resolve(this.modules.get(id) || null)
          } else {
            setTimeout(checkLoaded, 100)
          }
        }
        checkLoaded()
      })
    }

    const module = this.modules.get(id)
    if (!module) {
      console.error(`Module ${id} not found`)
      return null
    }

    this.loadingModules.add(id)

    try {
      // 检查依赖
      if (module.config.dependencies) {
        for (const depId of module.config.dependencies) {
          await this.load(depId)
        }
      }

      // 初始化模块
      if (module.initialize) {
        await module.initialize()
      }

      this.loadedModules.add(id)
      this.loadingModules.delete(id)
      
      console.log(`Module ${id} loaded successfully`)
      return module
    } catch (error) {
      this.loadingModules.delete(id)
      console.error(`Failed to load module ${id}:`, error)
      return null
    }
  }

  /**
   * 卸载模块
   */
  async unload(id: string): Promise<void> {
    const module = this.modules.get(id)
    if (!module) {
      return
    }

    try {
      if (module.destroy) {
        await module.destroy()
      }

      this.loadedModules.delete(id)
      console.log(`Module ${id} unloaded successfully`)
    } catch (error) {
      console.error(`Failed to unload module ${id}:`, error)
    }
  }

  /**
   * 检查模块是否已加载
   */
  isLoaded(id: string): boolean {
    return this.loadedModules.has(id)
  }

  /**
   * 获取所有路由
   */
  getAllRoutes(): RouteObject[] {
    const routes: RouteObject[] = []
    
    for (const module of this.getEnabled()) {
      if (module.routes) {
        routes.push(...module.routes)
      }
    }

    return routes
  }
}

// 全局模块注册表实例
export const moduleRegistry = new ModuleRegistry()

// 懒加载组件包装器
export function createLazyComponent(
  importFn: () => Promise<{ default: ComponentType<any> }>,
  fallback?: React.ReactNode
) {
  const LazyComponent = lazy(importFn)
  
  return function WrappedComponent(props: any) {
    return (
      <Suspense fallback={fallback || <div>Loading...</div>}>
        <LazyComponent {...props} />
      </Suspense>
    )
  }
}

// 模块加载器组件
export interface ModuleLoaderProps {
  moduleId: string
  fallback?: React.ReactNode
  onError?: (error: Error) => void
}

export function ModuleLoader({ moduleId, fallback, onError }: ModuleLoaderProps) {
  const [module, setModule] = React.useState<Module | null>(null)
  const [loading, setLoading] = React.useState(true)
  const [error, setError] = React.useState<Error | null>(null)

  React.useEffect(() => {
    let mounted = true

    const loadModule = async () => {
      try {
        setLoading(true)
        setError(null)
        
        const loadedModule = await moduleRegistry.load(moduleId)
        
        if (mounted) {
          setModule(loadedModule)
          setLoading(false)
        }
      } catch (err) {
        const error = err instanceof Error ? err : new Error('Unknown error')
        
        if (mounted) {
          setError(error)
          setLoading(false)
          onError?.(error)
        }
      }
    }

    loadModule()

    return () => {
      mounted = false
    }
  }, [moduleId, onError])

  if (loading) {
    return <>{fallback || <div>Loading module...</div>}</>
  }

  if (error) {
    return <div>Error loading module: {error.message}</div>
  }

  if (!module || !module.component) {
    return <div>Module not found or has no component</div>
  }

  const Component = module.component
  return <Component />
}
