/**
 * @file 用户管理API Mock处理器
 * @description 模拟用户管理相关的API响应
 * @status 框架文件 - 完成
 */

import { http, HttpResponse } from 'msw'
import { mockUsers } from '../data/users'
import { ApiResponse, User, PaginatedResponse, QueryParams } from '@/api/types'

export const userHandlers = [
  // 获取用户列表（分页）
  http.get('/api/users', async ({ request }) => {
    const url = new URL(request.url)
    const page = parseInt(url.searchParams.get('page') || '1')
    const pageSize = parseInt(url.searchParams.get('pageSize') || '10')
    const search = url.searchParams.get('search') || ''

    // 搜索过滤
    let filteredUsers = mockUsers
    if (search) {
      filteredUsers = mockUsers.filter(user =>
        user.username.toLowerCase().includes(search.toLowerCase()) ||
        user.email.toLowerCase().includes(search.toLowerCase()) ||
        user.displayName?.toLowerCase().includes(search.toLowerCase())
      )
    }

    // 分页
    const total = filteredUsers.length
    const totalPages = Math.ceil(total / pageSize)
    const startIndex = (page - 1) * pageSize
    const endIndex = startIndex + pageSize
    const items = filteredUsers.slice(startIndex, endIndex)

    const response: ApiResponse<PaginatedResponse<User>> = {
      success: true,
      data: {
        items,
        total,
        page,
        pageSize,
        totalPages,
      },
      message: '获取用户列表成功',
    }

    return HttpResponse.json(response)
  }),

  // 获取单个用户
  http.get('/api/users/:id', async ({ params }) => {
    const { id } = params
    const user = mockUsers.find(u => u.id === id)

    if (!user) {
      return HttpResponse.json({
        success: false,
        message: '用户不存在',
        code: 'USER_NOT_FOUND',
      }, { status: 404 })
    }

    const response: ApiResponse<User> = {
      success: true,
      data: user,
      message: '获取用户信息成功',
    }

    return HttpResponse.json(response)
  }),

  // 创建用户
  http.post('/api/users', async ({ request }) => {
    const userData = await request.json() as any

    // 检查用户名是否已存在
    const existingUser = mockUsers.find(u => u.username === userData.username)
    if (existingUser) {
      return HttpResponse.json({
        success: false,
        message: '用户名已存在',
        code: 'USERNAME_EXISTS',
      }, { status: 400 })
    }

    // 创建新用户
    const newUser: User = {
      id: `user_${Date.now()}`,
      username: userData.username,
      email: userData.email,
      displayName: userData.displayName || userData.username,
      roles: userData.roles || ['user'],
      permissions: ['read'],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isActive: true,
    }

    mockUsers.push(newUser)

    const response: ApiResponse<User> = {
      success: true,
      data: newUser,
      message: '用户创建成功',
    }

    return HttpResponse.json(response, { status: 201 })
  }),

  // 更新用户
  http.put('/api/users/:id', async ({ params, request }) => {
    const { id } = params
    const updateData = await request.json() as any

    const userIndex = mockUsers.findIndex(u => u.id === id)
    if (userIndex === -1) {
      return HttpResponse.json({
        success: false,
        message: '用户不存在',
        code: 'USER_NOT_FOUND',
      }, { status: 404 })
    }

    // 更新用户信息
    const updatedUser = {
      ...mockUsers[userIndex],
      ...updateData,
      updatedAt: new Date().toISOString(),
    }
    mockUsers[userIndex] = updatedUser

    const response: ApiResponse<User> = {
      success: true,
      data: updatedUser,
      message: '用户更新成功',
    }

    return HttpResponse.json(response)
  }),

  // 删除用户
  http.delete('/api/users/:id', async ({ params }) => {
    const { id } = params

    const userIndex = mockUsers.findIndex(u => u.id === id)
    if (userIndex === -1) {
      return HttpResponse.json({
        success: false,
        message: '用户不存在',
        code: 'USER_NOT_FOUND',
      }, { status: 404 })
    }

    // 删除用户
    mockUsers.splice(userIndex, 1)

    const response: ApiResponse<null> = {
      success: true,
      data: null,
      message: '用户删除成功',
    }

    return HttpResponse.json(response)
  }),
]