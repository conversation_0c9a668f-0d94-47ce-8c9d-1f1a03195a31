/**
 * @file 主应用组件
 * @description 应用程序的根组件，负责路由管理和全局布局
 * @status 框架文件 - 完成
 */

import React, { useEffect, useRef, useState } from 'react'
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { Layout } from '@/components/layout/Layout'
import { EventBusProvider } from '@/utils/EventBus'
import { ErrorBoundary } from '@/components/common/ErrorBoundary'
import { DevTools } from '@/utils/DevTools'
import { moduleRegistry } from '@/utils/ModuleLoader'
import { dashboardModule } from '@/modules/dashboard'
import toast, { Toaster } from 'react-hot-toast'

function App() {
    const initialized = useRef(false)
    const [modulesLoaded, setModulesLoaded] = useState(false)

    useEffect(() => {
        // 防止在 React StrictMode 下重复初始化
        if (initialized.current) {
            return
        }
        initialized.current = true

        const initializeModules = async () => {
            try {
                // 注册模块
                moduleRegistry.register(dashboardModule)

                // 加载默认模块
                await moduleRegistry.load('dashboard')

                // 标记模块已加载，触发重新渲染
                setModulesLoaded(true)
            } catch (error) {
                console.error('Failed to load dashboard module:', error)
                toast.error('模块加载失败')
            }
        }

        initializeModules()
    }, [])

    return (
        <ErrorBoundary>
            <EventBusProvider>
                <Router
                    future={{
                        v7_startTransition: true,
                        v7_relativeSplatPath: true,
                    }}
                >
                    <Layout>
                        {!modulesLoaded ? (
                            <div className="flex items-center justify-center min-h-screen">
                                <div className="text-center">
                                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                                    <p className="text-gray-600">正在加载模块...</p>
                                </div>
                            </div>
                        ) : (
                            <Routes>
                                {/* 默认重定向到首页 */}
                                <Route path="/" element={<Navigate to="/dashboard" replace />} />

                                {/* 动态加载模块路由 */}
                                {moduleRegistry.getAllRoutes().map((route, index) => (
                                    <Route key={index} {...route} />
                                ))}

                                {/* 404 页面 */}
                                <Route path="*" element={<div className="p-6 text-center">页面未找到</div>} />
                            </Routes>
                        )}
                    </Layout>
                </Router>

                {/* 全局通知 */}
                <Toaster
                    position="top-right"
                    toastOptions={{
                        duration: 4000,
                        style: {
                            background: '#363636',
                            color: '#fff',
                        },
                    }}
                />

                {/* 开发工具 */}
                <DevTools />
            </EventBusProvider>
        </ErrorBoundary>
    )
}

export default App