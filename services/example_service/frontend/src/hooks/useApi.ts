/**
 * @file API请求Hook
 * @description 提供API请求的React hook，包含加载状态和错误处理
 * @status 框架文件 - 完成
 */

import { useState, useEffect, useCallback } from 'react'
import { ApiResponse, ApiError } from '@/api/types'
import { useEventEmitter } from '@/utils/EventBus'

interface UseApiState<T> {
  data: T | null
  loading: boolean
  error: ApiError | null
}

interface UseApiOptions {
  immediate?: boolean
  onSuccess?: (data: any) => void
  onError?: (error: ApiError) => void
}

export function useApi<T = any>(
  apiCall: () => Promise<ApiResponse<T>>,
  options: UseApiOptions = {}
) {
  const { immediate = true, onSuccess, onError } = options
  const emit = useEventEmitter()

  const [state, setState] = useState<UseApiState<T>>({
    data: null,
    loading: false,
    error: null,
  })

  const execute = useCallback(async () => {
    setState(prev => ({ ...prev, loading: true, error: null }))

    try {
      const response = await apiCall()
      setState(prev => ({
        ...prev,
        data: response.data,
        loading: false,
        error: null,
      }))

      onSuccess?.(response.data)
      
      if (response.message) {
        emit('global:success', { message: response.message })
      }

      return response.data
    } catch (error) {
      const apiError = error as ApiError
      setState(prev => ({
        ...prev,
        loading: false,
        error: apiError,
      }))

      onError?.(apiError)
      emit('global:error', {
        message: apiError.message,
        code: apiError.code,
      })

      throw error
    }
  }, [apiCall, onSuccess, onError, emit])

  const reset = useCallback(() => {
    setState({
      data: null,
      loading: false,
      error: null,
    })
  }, [])

  useEffect(() => {
    if (immediate) {
      execute()
    }
  }, [immediate, execute])

  return {
    ...state,
    execute,
    reset,
  }
}

// 用于手动触发的API hook
export function useLazyApi<T = any>(
  apiCall: () => Promise<ApiResponse<T>>,
  options: Omit<UseApiOptions, 'immediate'> = {}
) {
  return useApi(apiCall, { ...options, immediate: false })
}

// 用于分页数据的API hook
export function usePaginatedApi<T = any>(
  apiCall: (page: number, pageSize: number) => Promise<ApiResponse<{ items: T[], total: number, page: number, pageSize: number }>>,
  initialPage = 1,
  initialPageSize = 10
) {
  const [page, setPage] = useState(initialPage)
  const [pageSize, setPageSize] = useState(initialPageSize)

  const { data, loading, error, execute } = useApi(() => apiCall(page, pageSize), {
    immediate: false,
  })

  const refresh = useCallback(() => {
    execute()
  }, [execute])

  const goToPage = useCallback((newPage: number) => {
    setPage(newPage)
  }, [])

  const changePageSize = useCallback((newPageSize: number) => {
    setPageSize(newPageSize)
    setPage(1) // 重置到第一页
  }, [])

  useEffect(() => {
    execute()
  }, [page, pageSize, execute])

  return {
    data: data?.items || [],
    total: data?.total || 0,
    page,
    pageSize,
    totalPages: data ? Math.ceil(data.total / data.pageSize) : 0,
    loading,
    error,
    refresh,
    goToPage,
    changePageSize,
  }
} 