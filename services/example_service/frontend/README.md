# ThingsMore Frontend

基于 React + Vite + TypeScript + Tailwind CSS 的现代化前端框架模版。

## 📋 项目特性

- ⚡️ **Vite** - 极速的构建工具和开发服务器
- ⚛️ **React 18** - 最新的React版本，支持并发特性
- 🟦 **TypeScript** - 类型安全的JavaScript超集
- 🎨 **Tailwind CSS** - 实用优先的CSS框架
- 📡 **Event Bus** - 模块间解耦通信
- 🔄 **MSW** - Mock Service Worker，用于API模拟
- 🛡️ **错误边界** - 优雅的错误处理
- 📱 **响应式设计** - 支持移动端和桌面端
- 🧩 **模块化架构** - 组件和功能的清晰分离
- 🧪 **测试框架** - Vitest + Testing Library 完整测试方案
- 🎯 **代码分割** - 自动代码分割和懒加载
- 💾 **缓存策略** - 多层缓存管理系统
- 🔧 **开发工具** - 内置调试面板和开发工具
- 📚 **组件库** - 可复用的UI组件库
- 🚀 **PWA支持** - 渐进式Web应用功能
- 📖 **Storybook** - 组件文档和开发环境

## 🏗️ 项目结构

```
frontend/
├── public/                 # 静态资源
├── src/
│   ├── api/               # API相关
│   │   ├── client.ts      # HTTP客户端
│   │   └── types.ts       # API类型定义
│   ├── components/        # 通用组件
│   │   ├── common/        # 基础组件
│   │   └── layout/        # 布局组件
│   ├── hooks/             # 自定义React Hooks
│   ├── mocks/             # Mock数据和处理器
│   │   ├── data/          # 模拟数据
│   │   └── handlers/      # MSW处理器
│   ├── modules/           # 功能模块（待扩展）
│   ├── stores/            # 状态管理（本地模块状态）
│   ├── styles/            # 全局样式
│   ├── utils/             # 工具函数
│   ├── App.tsx            # 主应用组件
│   └── main.tsx           # 应用入口
├── index.html             # HTML模版
├── package.json           # 依赖配置
├── tailwind.config.js     # Tailwind配置
├── tsconfig.json          # TypeScript配置
└── vite.config.ts         # Vite配置
```

## 🚀 快速开始

### 环境要求

- Node.js 16+
- npm 7+ 或 yarn 1.22+ 或 pnpm 7+

### 安装依赖

```bash
# 使用 npm
npm install

# 或使用 yarn
yarn

# 或使用 pnpm
pnpm install
```

### 开发服务器

```bash
# 启动开发服务器
npm run dev

# 或
yarn dev

# 或
pnpm dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看应用。

### 测试

```bash
# 运行测试
npm run test

# 运行测试并生成覆盖率报告
npm run test:coverage

# 运行测试UI界面
npm run test:ui

# 监听模式运行测试
npm run test:watch
```

### 构建生产版本

```bash
# 构建生产版本
npm run build

# 预览生产构建
npm run preview

# 分析构建包大小
npm run analyze
```

### Storybook

```bash
# 启动 Storybook 开发服务器
npm run storybook

# 构建 Storybook 静态文件
npm run build-storybook
```

### 代码质量

```bash
# 代码检查
npm run lint

# 自动修复代码问题
npm run lint:fix

# 代码格式化
npm run format

# 检查代码格式
npm run format:check

# TypeScript 类型检查
npm run type-check
```

## 🏛️ 架构设计

### 1. 模块化架构

- **组件单一职责**: 每个组件专注于单一功能
- **模块解耦**: 通过Event Bus进行跨模块通信
- **本地状态管理**: 避免全局状态，每个模块管理自己的状态

### 2. Event Bus通信

使用类型安全的Event Bus系统进行组件间通信：

```typescript
// 发布事件
const emit = useEventEmitter()
emit('notification:show', {
  type: 'success',
  title: '操作成功',
  message: '数据已保存'
})

// 监听事件
useEventListener('notification:show', (data) => {
  console.log('收到通知:', data)
})
```

### 3. API层设计

- **统一的HTTP客户端**: 基于axios，包含拦截器和错误处理
- **类型安全**: 完整的TypeScript类型定义
- **自动重试**: 支持请求失败自动重试
- **Token管理**: 自动处理认证token和刷新

### 4. 错误处理

- **错误边界**: 捕获React组件错误
- **全局错误处理**: 通过Event Bus统一处理错误
- **用户友好**: 提供清晰的错误信息和恢复选项

## 🧩 开发指南

### 1. 添加新模块

在 `src/modules/` 下创建新模块：

```
src/modules/your-module/
├── components/           # 模块专用组件
├── hooks/               # 模块专用hooks
├── services/            # 模块API服务
├── stores/              # 模块状态管理
├── types.ts             # 模块类型定义
└── index.ts             # 模块导出
```

### 2. 组件开发规范

```typescript
/**
 * @file 组件名称
 * @description 组件功能描述
 * @status 开发状态 - 状态描述
 */

import React from 'react'

interface ComponentProps {
  // 定义组件props
}

export function Component({ }: ComponentProps) {
  // 组件实现
  return (
    <div>
      {/* 组件内容 */}
    </div>
  )
}
```

### 3. API服务

```typescript
// src/api/services/userService.ts
import apiClient from '@/api/client'
import { User, PaginatedResponse } from '@/api/types'

export const userService = {
  async getUsers(page = 1, pageSize = 10) {
    return apiClient.get<PaginatedResponse<User>>('/users', {
      params: { page, pageSize }
    })
  },

  async createUser(userData: CreateUserRequest) {
    return apiClient.post<User>('/users', userData)
  }
}
```

### 4. Mock数据

在开发环境中，MSW会自动拦截API请求并返回mock数据：

```typescript
// src/mocks/handlers/example.ts
import { rest } from 'msw'

export const exampleHandlers = [
  rest.get('/api/example', (req, res, ctx) => {
    return res(
      ctx.json({
        success: true,
        data: { message: 'Hello from mock!' }
      })
    )
  })
]
```

## 🎨 样式指南

### 1. Tailwind CSS类名

使用 `cn()` 函数合并类名，支持条件类名：

```typescript
import { cn } from '@/utils/common'

<button 
  className={cn(
    'btn btn-primary',
    isLoading && 'opacity-50 cursor-not-allowed',
    variant === 'outline' && 'btn-outline'
  )}
>
  按钮
</button>
```

### 2. 组件样式

利用Tailwind的组件层级定义可复用样式：

```css
/* src/styles/index.css */
@layer components {
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors;
  }
  
  .btn-primary {
    @apply bg-primary text-primary-foreground hover:bg-primary/90;
  }
}
```

## 🔧 配置说明

### Vite配置

- **路径别名**: 支持 `@/` 别名指向 `src/` 目录
- **代理设置**: 开发环境代理API请求到后端服务
- **构建优化**: 生产环境的构建优化配置

### TypeScript配置

- **严格模式**: 启用严格的类型检查
- **路径映射**: 支持路径别名的类型解析
- **现代语法**: 支持最新的ES特性

### Tailwind配置

- **设计系统**: 扩展的颜色、字体、间距等设计token
- **响应式**: 移动优先的响应式设计
- **动画**: 自定义动画效果

## 📚 最佳实践

### 1. 组件设计

- **单一职责**: 每个组件只负责一个功能
- **props最小化**: 减少组件的props数量
- **组合优于继承**: 使用组合模式构建复杂组件

### 2. 状态管理

- **本地优先**: 优先使用本地状态
- **合理共享**: 只在必要时共享状态
- **Event Bus**: 用于跨模块通信

### 3. 错误处理

- **try/catch**: 在异步操作中使用try/catch
- **错误边界**: 在组件层级设置错误边界
- **用户反馈**: 提供清晰的错误信息

### 4. 性能优化

- **懒加载**: 使用React.lazy进行代码分割
- **记忆化**: 合理使用useMemo和useCallback
- **避免重渲染**: 优化组件更新逻辑

## 🤝 贡献指南

1. **代码规范**: 遵循项目的代码规范和约定
2. **类型安全**: 确保TypeScript类型的完整性
3. **测试覆盖**: 为新功能添加必要的测试
4. **文档更新**: 及时更新相关文档

## 📄 许可证

本项目采用 MIT 许可证。 