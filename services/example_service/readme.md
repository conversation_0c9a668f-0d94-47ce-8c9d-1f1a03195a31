# 第一阶段：MVP 验证阶段详细安排

目标：实现一个支持基础流程的企业内部 RAG 产品 Demo，具备 Mock 聊天交互体验、Agent 配置能力和权限控制等关键能力。

---

## 1. UI 骨架搭建（预计 1 周）

### ✅ 目标：
构建出可运行的基础前端框架，支持 Agent 列表选择、聊天界面、反馈评分交互。

### 📌 任务拆解：
- 首页导航 + 应用中心视图
  - 左侧 App Grouping 列表（分组分类 Agent）
  - Agent 卡片：图标、名称、简介（支持悬浮预览）
- 聊天界面：
  - 通用聊天窗口 UI
  - 输入框 + 回车发送
  - AI 回复区域
  - 收藏按钮、反馈按钮（点赞/点踩）
- 多标签页支持（选做）：允许同时打开多个 Agent

### 🚩 输出物：
- 前端页面结构整体完成，能走通从选择 Agent 到发起对话再到反馈的基础流程。

---

## 2. 本地配置驱动 Agent 功能（预计 1 周）

### ✅ 目标：
使用 JSON 文件管理 Agent 配置，前端页面基于此动态渲染，无需接入真实后端。

### 📌 任务拆解：
- JSON 配置文件结构设计：包括
  - Agent ID / 名称 / 图标
  - 角色定义（System Prompt）
  - 模型参数（温度、top_p 等）
- 前端读取本地配置并渲染 Agent 列表
- 聊天窗口中可展示对应 Agent 配置（如角色设定）

### 🚩 输出物：
- Agent 管理能力以配置文件方式实现，支持热加载或页面刷新生效。

---

## 3. 权限控制初步方案（预计 1 周）

### ✅ 目标：
基于 Mock 用户身份，实现基础权限控制，限制部分 Agent 的可见性与访问权。

### 📌 任务拆解：
- Mock 登录接口，返回用户名/角色信息
- JSON Agent 配置中添加权限字段：
  - 可访问角色列表，如 `["admin", "sales"]`
- 前端在 Agent 列表展示时自动过滤不可访问项
- 聊天页面打开时进行权限二次校验（防止绕过）

### 🚩 输出物：
- 支持模拟用户身份登录
- 不同用户角色看到的 Agent 列表不同，访问控制生效

---

## 4. 埋点与日志系统（预计 1 周）

### ✅ 目标：
实现前后端简单的数据记录，便于后续评估 Agent 使用效果和交互体验。

### 📌 任务拆解：

#### 前端：
- 埋点 SDK 接入（或自定义上报接口）
- 埋点事件：
  - Agent 点击
  - 聊天发送
  - 收藏、点赞、点踩等行为

#### 后端（可选 Mock）：
- 接收上报的埋点请求（如 POST `/track`）
- 日志格式统一（时间戳、用户 ID、事件类型、Agent ID）

### 🚩 输出物：
- 能在浏览器开发工具中看到埋点事件发出
- 后端收集 JSON 日志（可暂存在本地）

---

## ✅ 阶段交付标准：

- [ ] 页面流程从“登录 → Agent 列表 → 聊天 → 反馈”完整走通
- [ ] Agent 列表和配置均基于本地 JSON 文件管理
- [ ] 不同用户角色看到的 Agent 不同
- [ ] 可通过浏览器查看埋点请求，后端可收集行为日志

---

## 💡 补充建议：
可额外准备一份：
- mock 用户账号示例（如 admin、employee）
- agent 配置 JSON 示例
- 反馈数据结构定义（便于后续评分/优化）

